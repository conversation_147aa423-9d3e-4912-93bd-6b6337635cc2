"""Credential authentication API endpoints."""

from fastapi import APIRouter, Depends, HTTPException, status

from modules.auth.application.credential_auth_service import CredentialAuthService
from modules.auth.interfaces.credential_auth_schemas import (
    ChangePasswordRequest,
    LoginResponse,
    UserLoginRequest,
    UserRegisterRequest,
    UserResponse,
)
from modules.auth.interfaces.dependencies import (
    get_credential_auth_service,
    get_current_user,
)
from modules.user.domain.user_models import User

router = APIRouter(prefix="/auth/credential", tags=["Credential Authentication"])


@router.post(
    "/register",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Register new user",
    description="Register a new user with username, email, and password",
)
def register_user(
    request: UserRegisterRequest,
    auth_service: CredentialAuthService = Depends(get_credential_auth_service),
) -> UserResponse:
    """Register a new user with username, email, and password."""
    try:
        user = auth_service.register_user(
            username=request.username,
            email=request.email,
            password=request.password,
        )
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            status=user.status.value,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e


@router.post(
    "/login",
    response_model=LoginResponse,
    summary="User login",
    description="Authenticate user with username/email and password",
)
def login_user(
    request: UserLoginRequest,
    auth_service: CredentialAuthService = Depends(get_credential_auth_service),
) -> LoginResponse:
    """Authenticate user with username/email and password."""
    try:
        user, access_token = auth_service.login(
            username_or_email=request.username_or_email,
            password=request.password,
        )

        user_response = UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            status=user.status.value,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
        )

        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user=user_response,
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e),
        ) from e


@router.get(
    "/me",
    response_model=UserResponse,
    summary="Get current user",
    description="Get current authenticated user information",
)
def get_current_user_info(
    current_user: User = Depends(get_current_user),
) -> UserResponse:
    """Get current authenticated user information."""
    return UserResponse(
        id=current_user.id,
        username=current_user.username,
        email=current_user.email,
        status=current_user.status.value,
        created_at=current_user.created_at.isoformat(),
        updated_at=current_user.updated_at.isoformat(),
    )


@router.post(
    "/change-password",
    status_code=status.HTTP_200_OK,
    summary="Change password",
    description="Change user password",
)
def change_password(
    request: ChangePasswordRequest,
    current_user: User = Depends(get_current_user),
    auth_service: CredentialAuthService = Depends(get_credential_auth_service),
) -> dict:
    """Change user password."""
    try:
        success = auth_service.change_password(
            user_id=current_user.id,
            current_password=request.current_password,
            new_password=request.new_password,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid current password",
            )

        return {"message": "Password changed successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
