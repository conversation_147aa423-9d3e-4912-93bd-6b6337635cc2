"""Tests for credential authentication API endpoints."""

import uuid

from fastapi.testclient import TestClient


class TestCredentialAuthAPI:
    """Test cases for credential authentication API."""

    def should_register_user_when_valid_data_provided(
        self, test_client: TestClient
    ) -> None:
        """Test that user is registered successfully with valid data."""
        # Use random suffix to avoid conflicts
        random_suffix = str(uuid.uuid4())[:8]
        user_data = {
            "username": f"testuser_{random_suffix}",
            "email": f"test_{random_suffix}@example.com",
            "password": "securepassword123",
        }

        response = test_client.post("/api/v1/auth/credential/register", json=user_data)

        assert response.status_code == 201
        data = response.json()
        assert data["username"] == user_data["username"]
        assert data["email"] == user_data["email"]
        assert data["status"] == "active"
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data

    def should_reject_registration_when_username_already_exists(
        self, test_client: TestClient
    ) -> None:
        """Test that registration is rejected when username already exists."""
        # Use random suffix to avoid conflicts with other tests
        random_suffix = str(uuid.uuid4())[:8]
        # First registration
        user_data = {
            "username": f"existinguser_{random_suffix}",
            "email": f"first_{random_suffix}@example.com",
            "password": "password123",
        }
        test_client.post("/api/v1/auth/credential/register", json=user_data)

        # Second registration with same username
        duplicate_data = {
            "username": f"existinguser_{random_suffix}",
            "email": f"second_{random_suffix}@example.com",
            "password": "password456",
        }
        response = test_client.post(
            "/api/v1/auth/credential/register", json=duplicate_data
        )

        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def should_reject_registration_when_email_already_exists(
        self, test_client: TestClient
    ) -> None:
        """Test that registration is rejected when email already exists."""
        # Use random suffix to avoid conflicts with other tests
        random_suffix = str(uuid.uuid4())[:8]
        # First registration
        user_data = {
            "username": f"user1_{random_suffix}",
            "email": f"duplicate_{random_suffix}@example.com",
            "password": "password123",
        }
        test_client.post("/api/v1/auth/credential/register", json=user_data)

        # Second registration with same email
        duplicate_data = {
            "username": f"user2_{random_suffix}",
            "email": f"duplicate_{random_suffix}@example.com",
            "password": "password456",
        }
        response = test_client.post(
            "/api/v1/auth/credential/register", json=duplicate_data
        )

        assert response.status_code == 400
        assert "already exists" in response.json()["detail"]

    def should_return_access_token_when_valid_credentials_provided(
        self, test_client: TestClient
    ) -> None:
        """Test that access token is returned when valid credentials are provided."""
        # Use random suffix to avoid conflicts with other tests
        random_suffix = str(uuid.uuid4())[:8]
        # Register user first
        user_data = {
            "username": f"loginuser_{random_suffix}",
            "email": f"login_{random_suffix}@example.com",
            "password": "loginpassword",
        }
        test_client.post("/api/v1/auth/credential/register", json=user_data)

        # Login with username
        login_data = {
            "username_or_email": f"loginuser_{random_suffix}",
            "password": "loginpassword",
        }
        response = test_client.post("/api/v1/auth/credential/login", json=login_data)

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["username"] == f"loginuser_{random_suffix}"

    def should_return_access_token_when_valid_email_credentials_provided(
        self, test_client: TestClient
    ) -> None:
        """Test that access token is returned when valid email credentials are provided."""
        # Use random suffix to avoid conflicts with other tests
        random_suffix = str(uuid.uuid4())[:8]
        # Register user first
        user_data = {
            "username": f"emailuser_{random_suffix}",
            "email": f"email_{random_suffix}@example.com",
            "password": "emailpassword",
        }
        test_client.post("/api/v1/auth/credential/register", json=user_data)

        # Login with email
        login_data = {
            "username_or_email": f"email_{random_suffix}@example.com",
            "password": "emailpassword",
        }
        response = test_client.post("/api/v1/auth/credential/login", json=login_data)

        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert data["token_type"] == "bearer"
        assert "user" in data
        assert data["user"]["email"] == f"email_{random_suffix}@example.com"

    def should_deny_access_when_invalid_credentials_provided(
        self, test_client: TestClient
    ) -> None:
        """Test that access is denied when invalid credentials are provided."""
        login_data = {
            "username_or_email": "wronguser",
            "password": "wrongpassword",
        }
        response = test_client.post("/api/v1/auth/credential/login", json=login_data)

        assert response.status_code == 401
        assert "detail" in response.json()

    def should_return_current_user_info_when_valid_token_provided(
        self, test_client: TestClient
    ) -> None:
        """Test that current user info is returned with valid token."""
        import uuid

        # Register and login user with unique username
        unique_id = str(uuid.uuid4())[:8]
        user_data = {
            "username": f"currentuser_{unique_id}",
            "email": f"current_{unique_id}@example.com",
            "password": "currentpassword",
        }
        test_client.post("/api/v1/auth/credential/register", json=user_data)

        login_data = {
            "username_or_email": f"currentuser_{unique_id}",
            "password": "currentpassword",
        }
        login_response = test_client.post(
            "/api/v1/auth/credential/login", json=login_data
        )
        token = login_response.json()["access_token"]

        # Get current user info
        response = test_client.get(
            "/api/v1/auth/credential/me", headers={"Authorization": f"Bearer {token}"}
        )

        assert response.status_code == 200
        data = response.json()
        assert data["username"] == f"currentuser_{unique_id}"
        assert data["email"] == f"current_{unique_id}@example.com"
        assert data["status"] == "active"

    def should_deny_access_to_protected_route_without_token(
        self, test_client: TestClient
    ) -> None:
        """Test that protected route denies access without token."""
        response = test_client.get("/api/v1/auth/credential/me")

        assert response.status_code == 403

    def should_deny_access_to_protected_route_with_invalid_token(
        self, test_client: TestClient
    ) -> None:
        """Test that protected route denies access with invalid token."""
        response = test_client.get(
            "/api/v1/auth/credential/me",
            headers={"Authorization": "Bearer invalid_token"},
        )

        assert response.status_code == 401

    def should_change_password_when_valid_current_password_provided(
        self, test_client: TestClient
    ) -> None:
        """Test that password is changed successfully with valid current password."""
        import uuid

        # Register and login user with unique username
        unique_id = str(uuid.uuid4())[:8]
        user_data = {
            "username": f"changeuser_{unique_id}",
            "email": f"change_{unique_id}@example.com",
            "password": "oldpassword",
        }
        test_client.post("/api/v1/auth/credential/register", json=user_data)

        login_data = {
            "username_or_email": f"changeuser_{unique_id}",
            "password": "oldpassword",
        }
        login_response = test_client.post(
            "/api/v1/auth/credential/login", json=login_data
        )
        token = login_response.json()["access_token"]

        # Change password
        change_data = {
            "current_password": "oldpassword",
            "new_password": "newpassword123",
        }
        response = test_client.post(
            "/api/v1/auth/credential/change-password",
            json=change_data,
            headers={"Authorization": f"Bearer {token}"},
        )

        assert response.status_code == 200
        assert response.json()["message"] == "Password changed successfully"

        # Verify old password no longer works
        old_login_data = {
            "username_or_email": f"changeuser_{unique_id}",
            "password": "oldpassword",
        }
        old_response = test_client.post(
            "/api/v1/auth/credential/login", json=old_login_data
        )
        assert old_response.status_code == 401

        # Verify new password works
        new_login_data = {
            "username_or_email": f"changeuser_{unique_id}",
            "password": "newpassword123",
        }
        new_response = test_client.post(
            "/api/v1/auth/credential/login", json=new_login_data
        )
        assert new_response.status_code == 200

    def should_reject_password_change_when_invalid_current_password_provided(
        self, test_client: TestClient
    ) -> None:
        """Test that password change is rejected with invalid current password."""
        import uuid

        # Register and login user with unique username
        unique_id = str(uuid.uuid4())[:8]
        user_data = {
            "username": f"wronguser_{unique_id}",
            "email": f"wrong_{unique_id}@example.com",
            "password": "correctpassword",
        }
        test_client.post("/api/v1/auth/credential/register", json=user_data)

        login_data = {
            "username_or_email": f"wronguser_{unique_id}",
            "password": "correctpassword",
        }
        login_response = test_client.post(
            "/api/v1/auth/credential/login", json=login_data
        )
        token = login_response.json()["access_token"]

        # Try to change password with wrong current password
        change_data = {
            "current_password": "wrongpassword",
            "new_password": "newpassword123",
        }
        response = test_client.post(
            "/api/v1/auth/credential/change-password",
            json=change_data,
            headers={"Authorization": f"Bearer {token}"},
        )

        assert response.status_code == 400
        assert "Invalid current password" in response.json()["detail"]
