"""OAuth Provider repository interfaces."""

from abc import ABC, abstractmethod
from uuid import UUID

from .oauth_provider_models import OAuthProviderConfig


class OAuthProviderRepository(ABC):
    """Repository interface for OAuth provider configuration operations."""

    @abstractmethod
    def create(self, provider_config: OAuthProviderConfig) -> OAuthProviderConfig:
        """Create a new OAuth provider configuration."""

    @abstractmethod
    def get_by_name(self, name: str) -> OAuthProviderConfig | None:
        """Get OAuth provider configuration by name."""

    @abstractmethod
    def get_by_id(self, provider_id: UUID) -> OAuthProviderConfig | None:
        """Get OAuth provider configuration by ID."""

    @abstractmethod
    def get_all_enabled(self) -> list[OAuthProviderConfig]:
        """Get all enabled OAuth provider configurations."""

    @abstractmethod
    def get_all(self) -> list[OAuthProviderConfig]:
        """Get all OAuth provider configurations."""

    @abstractmethod
    def update(self, provider_config: OAuthProviderConfig) -> OAuthProviderConfig:
        """Update an OAuth provider configuration."""

    @abstractmethod
    def delete(self, provider_id: UUID) -> bool:
        """Delete an OAuth provider configuration."""

    @abstractmethod
    def exists_by_name(self, name: str) -> bool:
        """Check if a provider with the given name exists."""
