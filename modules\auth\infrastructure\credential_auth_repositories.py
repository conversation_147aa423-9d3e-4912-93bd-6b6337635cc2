"""Credential authentication repository implementations."""

from uuid import UUID

from sqlalchemy import or_, select
from sqlalchemy.orm import Session

from modules.auth.domain.credential_auth_repositories import UserRepository
from modules.user.domain.user_models import User, UserStatus
from modules.user.infrastructure.user_orm import UserORM


class UserRepositoryImpl(UserRepository):
    """SQLAlchemy implementation of user repository."""

    def __init__(self, session: Session):
        self.session = session

    def get_by_id(self, user_id: UUID) -> User | None:
        """Get user by ID."""
        stmt = select(UserORM).where(UserORM.id == user_id)
        result = self.session.execute(stmt)
        user_orm = result.scalar_one_or_none()

        return self._orm_to_domain(user_orm) if user_orm else None

    def get_by_username(self, username: str) -> User | None:
        """Get user by username."""
        stmt = select(UserORM).where(UserORM.username == username)
        result = self.session.execute(stmt)
        user_orm = result.scalar_one_or_none()

        return self._orm_to_domain(user_orm) if user_orm else None

    def get_by_email(self, email: str) -> User | None:
        """Get user by email."""
        stmt = select(UserORM).where(UserORM.email == email)
        result = self.session.execute(stmt)
        user_orm = result.scalar_one_or_none()

        return self._orm_to_domain(user_orm) if user_orm else None

    def get_by_username_or_email(self, username_or_email: str) -> User | None:
        """Get user by username or email."""
        stmt = select(UserORM).where(
            or_(
                UserORM.username == username_or_email,
                UserORM.email == username_or_email,
            )
        )
        result = self.session.execute(stmt)
        user_orm = result.scalar_one_or_none()

        return self._orm_to_domain(user_orm) if user_orm else None

    def create(self, user: User) -> User:
        """Create a new user."""
        user_orm = UserORM(
            id=user.id,
            username=user.username,
            email=user.email,
            hashed_password=user.hashed_password,
            status=user.status,
            created_at=user.created_at,
            updated_at=user.updated_at,
        )

        self.session.add(user_orm)
        self.session.flush()
        self.session.refresh(user_orm)

        return self._orm_to_domain(user_orm)

    def update(self, user: User) -> User:
        """Update an existing user."""
        stmt = select(UserORM).where(UserORM.id == user.id)
        result = self.session.execute(stmt)
        user_orm = result.scalar_one_or_none()

        if not user_orm:
            raise ValueError(f"User with ID {user.id} not found")

        # Update fields
        user_orm.username = user.username
        user_orm.email = user.email
        user_orm.hashed_password = user.hashed_password
        user_orm.status = user.status
        user_orm.updated_at = user.updated_at

        self.session.flush()
        self.session.refresh(user_orm)

        return self._orm_to_domain(user_orm)

    def delete(self, user_id: UUID) -> bool:
        """Delete a user."""
        stmt = select(UserORM).where(UserORM.id == user_id)
        result = self.session.execute(stmt)
        user_orm = result.scalar_one_or_none()

        if not user_orm:
            return False

        self.session.delete(user_orm)
        self.session.flush()

        return True

    def update_status(self, user_id: UUID, status: UserStatus) -> bool:
        """Update user status."""
        stmt = select(UserORM).where(UserORM.id == user_id)
        result = self.session.execute(stmt)
        user_orm = result.scalar_one_or_none()

        if not user_orm:
            return False

        user_orm.status = status
        self.session.flush()

        return True

    def exists_by_username(self, username: str) -> bool:
        """Check if username exists."""
        stmt = select(UserORM.id).where(UserORM.username == username)
        result = self.session.execute(stmt)
        return result.scalar_one_or_none() is not None

    def exists_by_email(self, email: str) -> bool:
        """Check if email exists."""
        stmt = select(UserORM.id).where(UserORM.email == email)
        result = self.session.execute(stmt)
        return result.scalar_one_or_none() is not None

    def _orm_to_domain(self, user_orm: UserORM) -> User:
        """Convert ORM object to domain model."""
        return User(
            id=user_orm.id,
            username=user_orm.username,
            email=user_orm.email,
            hashed_password=user_orm.hashed_password,
            status=user_orm.status,
            created_at=user_orm.created_at,
            updated_at=user_orm.updated_at,
        )
