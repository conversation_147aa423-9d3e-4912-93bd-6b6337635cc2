"""
Requirements Generator Agent

Intelligent agent for generating detailed technical development requirements.
"""

import os
import re
from typing import Any, Dict, List, Optional
from datetime import datetime
from pathlib import Path

from ..core.base_agent import BaseAgent, AgentResult, WorkflowContext
from ..utils.config_manager import ConfigManager


class RequirementsGeneratorAgent(BaseAgent):
    """Agent for generating detailed technical development requirements."""
    
    def __init__(self, llm=None, verbose: bool = False, stream_displayer=None, log_dir=None):
        super().__init__(
            name="requirements_generator",
            llm=llm,
            verbose=verbose,
            stream_displayer=stream_displayer,
            log_dir=log_dir
        )
        self.config_manager = ConfigManager()
        self.requirements_prompt_config = self._load_prompt_config("requirements_generation")

    def _load_prompt_config(self, prompt_name: str) -> Dict[str, Any]:
        """Load prompt configuration from YAML file."""
        try:
            import yaml
            prompt_file = Path(__file__).parent.parent / "prompts" / f"{prompt_name}.yaml"
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                return {}
        except Exception:
            return {}

    def get_system_prompt(self) -> str:
        """Get the system prompt for requirements generation."""
        return self.requirements_prompt_config.get("system_prompt", "")
    
    def _extract_user_stories_from_business_analysis(self, business_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract user stories from business analysis."""
        user_stories = []

        # Get the raw content from business analysis
        raw_content = ""
        if isinstance(business_analysis, dict):
            # Try different possible keys for content
            raw_content = (business_analysis.get("raw_markdown", "") or
                          business_analysis.get("content", "") or
                          business_analysis.get("data", "") or
                          str(business_analysis))
        elif isinstance(business_analysis, str):
            raw_content = business_analysis
        else:
            raw_content = str(business_analysis)

        if not raw_content:
            print("Warning: No content found in business analysis")
            return user_stories

        print(f"Extracting user stories from content length: {len(raw_content)}")

        # Extract user stories using multiple patterns to handle different formats
        patterns = [
            # Pattern 1: Chinese format with 验收标准
            r'\*\*US-(\d+):\s*([^*\n]+)\*\*\s*\n\s*-?\s*\*\*验收标准\*\*?[：:]?\s*(.*?)(?=\n\s*-?\s*\*\*US-|\n\s*###|\n\s*##|\n\s*\*\*\w+\*\*|\Z)',
            # Pattern 2: English format with acceptance criteria
            r'\*\*US-(\d+):\s*([^*\n]+)\*\*\s*\n\s*-?\s*\*\*验收标准\*\*?[：:]?\s*(.*?)(?=\n\s*-?\s*\*\*US-|\n\s*###|\n\s*##|\n\s*\*\*\w+\*\*|\Z)',
            # Pattern 3: Simple format - just US-XXX: title
            r'- \*\*US-(\d+):\s*([^*\n]+)\*\*',
            # Pattern 4: Even simpler format
            r'US-(\d+):\s*([^\n]+)'
        ]

        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, raw_content, re.DOTALL | re.MULTILINE)
            print(f"Pattern {i+1} found {len(matches)} matches")

            if matches:
                for match in matches:
                    if len(match) >= 2:
                        story_id = match[0].strip()
                        title = match[1].strip()
                        acceptance_criteria = match[2].strip() if len(match) > 2 else ""

                        user_story = {
                            "id": f"US-{story_id.zfill(3)}",
                            "title": title,
                            "description": title,  # Use title as description for now
                            "acceptance_criteria": acceptance_criteria,
                            "priority": "high" if int(story_id) <= 5 else "medium" if int(story_id) <= 7 else "low"
                        }

                        user_stories.append(user_story)
                        print(f"Extracted user story: {user_story['id']} - {user_story['title'][:50]}...")

                # If we found stories with this pattern, stop trying other patterns
                if user_stories:
                    break

        # Fallback: extract from structured data if available
        if not user_stories and isinstance(business_analysis, dict):
            stories_data = business_analysis.get("user_stories", [])
            for i, story in enumerate(stories_data, 1):
                user_stories.append({
                    "id": f"US-{i:03d}",
                    "title": story.get("title", f"User Story {i}"),
                    "description": story.get("description", ""),
                    "acceptance_criteria": story.get("acceptance_criteria", ""),
                    "priority": story.get("priority", "medium")
                })

        # If no user stories found, create default ones
        if not user_stories:
            user_stories = [
                {
                    "id": "US-001",
                    "title": "基础功能实现",
                    "description": "作为用户，我希望系统提供基础功能，以便完成核心业务流程",
                    "acceptance_criteria": "系统能够正常启动并提供基本API接口",
                    "priority": "high"
                }
            ]

        return user_stories
    
    def _create_output_directory(self, context: WorkflowContext) -> Path:
        """Create output directory for requirements and prompts."""
        # Use the log_dir from agent if available, otherwise fallback to project root
        if hasattr(self, 'log_dir') and self.log_dir:
            # Extract the parent directory of log_dir (which should be the timestamped output dir)
            output_base = Path(self.log_dir).parent / "results"
        else:
            # Fallback to project root with ai_generated (for backward compatibility)
            output_base = Path(context.project_root) / "ai_generated"

        output_dir = output_base / "requirements"
        output_dir.mkdir(parents=True, exist_ok=True)
        return output_dir
    
    def _generate_requirement_for_user_story(self, user_story: Dict[str, Any], domain_model: Dict[str, Any],
                                           context: WorkflowContext, story_index: int) -> str:
        """Generate requirement document for a specific user story."""

        story_id = user_story.get("id", f"US-{story_index:03d}")
        story_title = user_story.get("title", "")
        story_description = user_story.get("description", "")
        acceptance_criteria = user_story.get("acceptance_criteria", "")

        # Prepare user story-specific input
        user_input = f"""
请基于以下用户故事生成详细的技术开发需求：

=== 用户故事信息 ===
**故事ID**: {story_id}
**故事标题**: {story_title}
**故事描述**: {story_description}
**验收标准**: {acceptance_criteria}

=== 相关领域模型 ===
{self._format_domain_model_for_story(domain_model, user_story)}

=== 项目约束 ===
- 架构风格: {context.architecture_style}
- 技术栈: {', '.join(context.tech_stack)}
- 现有模块: {', '.join(context.existing_modules)}

=== 生成要求 ===
1. 专注于该用户故事的具体实现需求
2. 明确定义API接口设计（如果涉及）
3. 数据模型设计（如果涉及）
4. 业务逻辑实现要点
5. 测试策略和验收测试用例
6. 实现的技术难点和解决方案
7. 与其他用户故事的依赖关系

请以Markdown格式输出，内容要详细且技术方案可行。
"""

        # Execute LLM call with streaming
        system_prompt = self._get_story_system_prompt(context)
        messages = self._create_messages(system_prompt, user_input)
        response = self._execute_llm_call_with_streaming(messages, f"生成用户故事 {story_id} 开发需求")

        return response
    
    def _generate_prompt_for_user_story(self, user_story: Dict[str, Any], requirement_content: str,
                                      story_index: int) -> str:
        """Generate AI development prompt for a specific user story."""

        story_id = user_story.get("id", f"US-{story_index:03d}")
        story_title = user_story.get("title", "")

        prompt_content = f"""# AI 开发提示词 - {story_id}: {story_title}

## 任务概述
你是一个专业的 Python 后端开发工程师，需要基于以下用户故事的开发需求实现完整功能。

## 用户故事信息
- **故事ID**: {story_id}
- **故事标题**: {story_title}
- **故事描述**: {user_story.get("description", "")}
- **验收标准**: {user_story.get("acceptance_criteria", "")}

## 详细开发需求
{requirement_content}

## 开发约束
1. **架构要求**: 严格遵循 DDD 四层架构 (interfaces/application/domain/infrastructure)
2. **代码规范**: 遵循 PEP 8 规范，使用类型提示
3. **技术栈**: FastAPI + SQLAlchemy + Pydantic + Alembic + Pytest
4. **数据库**: 所有实体ID使用UUID，字段名只反映业务含义
5. **测试**: 必须包含单元测试、集成测试和API测试
6. **文档**: 所有注释和文档使用英文

## 实现要求
1. 专注于该用户故事的具体功能实现
2. 按照 DDD 分层架构组织代码
3. 实现完整的业务逻辑和API接口
4. 提供完整的测试用例覆盖验收标准
5. 确保代码质量和可维护性
6. 遵循项目现有的代码风格和约定

## 输出格式
请按照以下顺序实现：
1. Domain 层 (实体、值对象、仓库接口) - 如果涉及新的领域概念
2. Infrastructure 层 (ORM模型、仓库实现) - 如果涉及数据持久化
3. Application 层 (应用服务、用例) - 业务逻辑实现
4. Interfaces 层 (API路由、Schema) - 如果涉及API接口
5. 测试代码 (单元测试、集成测试、API测试) - 覆盖验收标准

每个文件都要包含完整的实现和必要的注释。
"""

        return prompt_content
    
    def _format_domain_model_for_story(self, domain_model: Dict[str, Any], user_story: Dict[str, Any]) -> str:
        """Format domain model data relevant to a specific user story."""

        # Check if this is markdown format
        if domain_model.get("content_type") == "markdown":
            return self._extract_story_relevant_markdown(domain_model, user_story)

        # Legacy JSON format handling
        formatted = []

        # Add bounded contexts
        bounded_contexts = domain_model.get("bounded_contexts", [])
        for context in bounded_contexts:
            if self._is_context_relevant_to_story(context, user_story):
                formatted.append(f"边界上下文: {context.get('name', '')}")
                formatted.append(f"职责: {', '.join(context.get('responsibilities', []))}")

        # Add relevant aggregates
        aggregates = domain_model.get("aggregates", [])
        for aggregate in aggregates:
            if self._is_aggregate_relevant_to_story(aggregate, user_story):
                formatted.append(f"\n聚合: {aggregate.get('name', '')}")
                formatted.append(f"聚合根: {aggregate.get('aggregate_root', '')}")
                formatted.append(f"实体: {', '.join(aggregate.get('entities', []))}")

        # Add relevant entities
        entities = domain_model.get("domain_entities", [])
        for entity in entities:
            if self._is_entity_relevant_to_story(entity, user_story):
                formatted.append(f"\n实体: {entity.get('name', '')}")
                formatted.append(f"描述: {entity.get('description', '')}")

        return "\n".join(formatted) if formatted else "无相关领域模型数据"

    def _extract_story_relevant_markdown(self, domain_model: Dict[str, Any], user_story: Dict[str, Any]) -> str:
        """Extract story-relevant content from markdown domain model."""
        raw_markdown = domain_model.get("raw_markdown", "")
        sections = domain_model.get("sections", {})

        # For now, return the full markdown content
        # In the future, we could implement more sophisticated filtering based on story content
        return raw_markdown
    
    def _is_context_relevant_to_story(self, context: Dict[str, Any], user_story: Dict[str, Any]) -> bool:
        """Check if a bounded context is relevant to the user story."""
        context_name = context.get("name", "").lower()
        story_content = f"{user_story.get('title', '')} {user_story.get('description', '')}".lower()

        # Check for keyword matches
        if any(keyword in story_content for keyword in ["用户", "user", "登录", "注册", "认证"]):
            if "用户" in context_name or "user" in context_name or "身份" in context_name:
                return True

        if any(keyword in story_content for keyword in ["服务器", "server", "mcp", "列表", "搜索", "发现"]):
            if "服务器" in context_name or "server" in context_name or "目录" in context_name:
                return True

        if any(keyword in story_content for keyword in ["评论", "反馈", "评分", "社区"]):
            if "反馈" in context_name or "社区" in context_name or "community" in context_name:
                return True

        return False

    def _is_aggregate_relevant_to_story(self, aggregate: Dict[str, Any], user_story: Dict[str, Any]) -> bool:
        """Check if an aggregate is relevant to the user story."""
        aggregate_name = aggregate.get("name", "").lower()
        story_content = f"{user_story.get('title', '')} {user_story.get('description', '')}".lower()

        # Check for keyword matches
        if any(keyword in story_content for keyword in ["用户", "user", "登录", "注册", "认证"]):
            if "用户" in aggregate_name or "user" in aggregate_name:
                return True

        if any(keyword in story_content for keyword in ["服务器", "server", "mcp", "列表", "搜索", "发现"]):
            if "服务器" in aggregate_name or "server" in aggregate_name:
                return True

        if any(keyword in story_content for keyword in ["评论", "反馈", "评分", "社区"]):
            if "反馈" in aggregate_name or "评论" in aggregate_name or "review" in aggregate_name:
                return True

        return False

    def _is_entity_relevant_to_story(self, entity: Dict[str, Any], user_story: Dict[str, Any]) -> bool:
        """Check if an entity is relevant to the user story."""
        entity_name = entity.get("name", "").lower()
        story_content = f"{user_story.get('title', '')} {user_story.get('description', '')}".lower()

        # Check for keyword matches
        if any(keyword in story_content for keyword in ["用户", "user", "登录", "注册", "认证"]):
            if "user" in entity_name or "用户" in entity_name:
                return True

        if any(keyword in story_content for keyword in ["服务器", "server", "mcp", "列表", "搜索", "发现"]):
            if "server" in entity_name or "服务器" in entity_name:
                return True

        if any(keyword in story_content for keyword in ["评论", "反馈", "评分", "社区"]):
            if "review" in entity_name or "feedback" in entity_name or "评论" in entity_name:
                return True

        return False
    
    def _get_story_system_prompt(self, context: Optional[WorkflowContext] = None) -> str:
        """Get system prompt for user story requirements generation, incorporating rules as context."""
        base_prompt = self.get_system_prompt()

        # Load project rules as context for LLM understanding
        rules_context = self._load_project_rules_as_context(context)

        # Combine base prompt with rules context
        enhanced_prompt = f"""{base_prompt}

## 项目规则与约束 (作为理解上下文)
以下规则用于指导你理解项目要求和生成高质量的开发需求，请在生成内容时遵循这些约束：

{rules_context}

## 重要说明
- 以上规则仅用于指导你理解项目架构和开发标准
- 生成的开发需求文档中不需要重复包含这些规则内容
- 专注于用户故事的具体实现需求，确保技术方案符合项目规范
"""

        return enhanced_prompt

    def _load_project_rules_as_context(self, context: Optional[WorkflowContext] = None) -> str:
        """Load project rules as context for LLM understanding, not for direct inclusion in output."""
        # Check if processed rules file is available in context
        processed_rules_file = None
        if context and hasattr(context, 'additional_context'):
            processed_rules_file = context.additional_context.get("processed_rules_file")

        # Load processed rules if available
        if processed_rules_file and Path(processed_rules_file).exists():
            try:
                processed_rules = Path(processed_rules_file).read_text(encoding='utf-8')
                self.logger.info(f"Using processed rules as context from: {processed_rules_file}")
                return processed_rules
            except Exception as e:
                self.logger.warning(f"Failed to load processed rules from {processed_rules_file}: {e}")

        # Fallback to original rules loading logic
        project_rules = ""
        try:
            rules_path = Path(".roo/rules/rules.md")
            if rules_path.exists():
                project_rules = rules_path.read_text(encoding='utf-8')
        except Exception:
            pass

        # Load agent-specific rules
        agent_rules = self.get_agent_rules()

        # Combine rules for context
        combined_rules = []
        if project_rules:
            combined_rules.append("### 项目特定规则\n" + project_rules)
        if agent_rules:
            combined_rules.append("### 开发标准与指南\n" + agent_rules)

        if combined_rules:
            return "\n\n".join(combined_rules)
        else:
            return "使用默认DDD架构规范和开发标准"

    def process(self, input_data: Dict[str, Any], context: WorkflowContext) -> AgentResult:
        """Process business analysis and generate technical requirements for user stories."""
        try:
            # Get business analysis data (which should contain user stories)
            business_analysis = input_data
            if not business_analysis:
                return AgentResult(
                    success=False,
                    data={},
                    metadata={"agent_name": self.name},
                    errors=["No business analysis data provided"],
                    execution_time=0.0,
                    timestamp=datetime.now()
                )

            start_time = datetime.now()

            # Extract user stories from business analysis
            user_stories = self._extract_user_stories_from_business_analysis(business_analysis)

            # Also get domain model if available in context
            domain_model = context.additional_context.get("domain_model", {}) if hasattr(context, 'additional_context') else {}

            # Create output directory
            output_dir = self._create_output_directory(context)

            # Generate requirements and prompts for each user story
            generated_files = []

            for i, user_story in enumerate(user_stories, 1):
                story_id = user_story.get("id", f"US-{i:03d}")
                story_title = user_story.get("title", "")

                # Generate requirement document
                requirement_content = self._generate_requirement_for_user_story(
                    user_story, domain_model, context, i
                )

                # Save requirement file
                requirement_filename = f"{story_id}_{self._sanitize_filename(story_title)}_requirements.md"
                requirement_path = output_dir / requirement_filename
                requirement_path.write_text(requirement_content, encoding='utf-8')
                generated_files.append(str(requirement_path))

                # Generate prompt document
                prompt_content = self._generate_prompt_for_user_story(
                    user_story, requirement_content, i
                )

                # Save prompt file
                prompt_filename = f"prompt_{story_id}_{self._sanitize_filename(story_title)}.md"
                prompt_path = output_dir / prompt_filename
                prompt_path.write_text(prompt_content, encoding='utf-8')
                generated_files.append(str(prompt_path))

                if self.verbose:
                    print(f"[SUCCESS] 已生成用户故事 {story_id} 的需求文档和提示词")

            execution_time = (datetime.now() - start_time).total_seconds()

            return AgentResult(
                success=True,
                data={
                    "user_stories": [story.get("id", f"US-{i:03d}") for i, story in enumerate(user_stories, 1)],
                    "generated_files": generated_files,
                    "output_directory": str(output_dir),
                    "file_count": len(generated_files),
                    "stories_processed": len(user_stories)
                },
                metadata={
                    "agent_name": self.name,
                    "stories_processed": len(user_stories),
                    "output_format": "markdown"
                },
                errors=[],
                execution_time=execution_time,
                timestamp=datetime.now()
            )

        except Exception as e:
            return AgentResult(
                success=False,
                data={},
                metadata={"agent_name": self.name},
                errors=[f"Error processing business analysis: {str(e)}"],
                execution_time=0.0,
                timestamp=datetime.now()
            )

    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename by removing invalid characters."""
        # Remove or replace invalid characters
        sanitized = re.sub(r'[^\w\s-]', '', filename)
        sanitized = re.sub(r'[-\s]+', '_', sanitized)
        return sanitized.strip('_')[:50]  # Limit length

    def _format_domain_model(self, domain_model: Dict[str, Any]) -> str:
        """Format domain model data for display."""
        formatted = []

        # Add bounded contexts
        bounded_contexts = domain_model.get("bounded_contexts", [])
        if bounded_contexts:
            formatted.append("=== 边界上下文 ===")
            for context in bounded_contexts:
                formatted.append(f"- {context.get('name', '')}: {', '.join(context.get('responsibilities', []))}")

        # Add aggregates
        aggregates = domain_model.get("aggregates", [])
        if aggregates:
            formatted.append("\n=== 聚合 ===")
            for aggregate in aggregates:
                formatted.append(f"- {aggregate.get('name', '')}")
                formatted.append(f"  聚合根: {aggregate.get('aggregate_root', '')}")
                formatted.append(f"  实体: {', '.join(aggregate.get('entities', []))}")

        # Add entities
        entities = domain_model.get("domain_entities", [])
        if entities:
            formatted.append("\n=== 领域实体 ===")
            for entity in entities:
                formatted.append(f"- {entity.get('name', '')}: {entity.get('description', '')}")

        return "\n".join(formatted) if formatted else "无领域模型数据"
