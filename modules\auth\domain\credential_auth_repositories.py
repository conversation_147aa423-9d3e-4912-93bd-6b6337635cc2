"""Credential authentication repository interfaces."""

from abc import ABC, abstractmethod
from uuid import UUID

from modules.user.domain.user_models import User, UserStatus


class UserRepository(ABC):
    """Repository interface for user operations."""

    @abstractmethod
    def get_by_id(self, user_id: UUID) -> User | None:
        """Get user by ID."""

    @abstractmethod
    def get_by_username(self, username: str) -> User | None:
        """Get user by username."""

    @abstractmethod
    def get_by_email(self, email: str) -> User | None:
        """Get user by email."""

    @abstractmethod
    def get_by_username_or_email(self, username_or_email: str) -> User | None:
        """Get user by username or email."""

    @abstractmethod
    def create(self, user: User) -> User:
        """Create a new user."""

    @abstractmethod
    def update(self, user: User) -> User:
        """Update an existing user."""

    @abstractmethod
    def delete(self, user_id: UUID) -> bool:
        """Delete a user."""

    @abstractmethod
    def update_status(self, user_id: UUID, status: UserStatus) -> bool:
        """Update user status."""

    @abstractmethod
    def exists_by_username(self, username: str) -> bool:
        """Check if username exists."""

    @abstractmethod
    def exists_by_email(self, email: str) -> bool:
        """Check if email exists."""
