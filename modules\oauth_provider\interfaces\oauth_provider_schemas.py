"""OAuth Provider management API schemas."""

from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, HttpUrl


class OAuthProviderCreateRequest(BaseModel):
    """Schema for creating a new OAuth provider configuration."""

    name: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Provider identifier (e.g., 'github', 'google')",
    )
    display_name: str = Field(
        ..., min_length=1, max_length=100, description="Human-readable provider name"
    )
    description: str | None = Field(None, description="Provider description")
    is_enabled: bool = Field(
        default=True, description="Whether this provider is enabled"
    )
    icon_url: HttpUrl | None = Field(None, description="URL to provider icon")

    # OAuth configuration
    client_id: str = Field(..., min_length=1, description="OAuth client ID")
    client_secret: str = Field(..., min_length=1, description="OAuth client secret")
    authorize_url: HttpUrl = Field(..., description="OAuth authorization endpoint URL")
    token_url: HttpUrl = Field(..., description="OAuth token exchange endpoint URL")
    user_info_url: HttpUrl = Field(..., description="User information endpoint URL")
    scope: str = Field(..., description="OAuth scope string")

    # User data mapping configuration
    user_id_field: str = Field(
        default="id", description="Field name for user ID in provider response"
    )
    email_field: str = Field(
        default="email", description="Field name for email in provider response"
    )
    name_field: str = Field(
        default="name", description="Field name for name in provider response"
    )
    username_field: str | None = Field(
        None, description="Field name for username in provider response"
    )
    avatar_field: str | None = Field(
        None, description="Field name for avatar URL in provider response"
    )


class OAuthProviderUpdateRequest(BaseModel):
    """Schema for updating an OAuth provider configuration."""

    display_name: str | None = Field(
        None, min_length=1, max_length=100, description="Human-readable provider name"
    )
    description: str | None = Field(None, description="Provider description")
    is_enabled: bool | None = Field(
        None, description="Whether this provider is enabled"
    )
    icon_url: HttpUrl | None = Field(None, description="URL to provider icon")

    # OAuth configuration
    client_id: str | None = Field(None, min_length=1, description="OAuth client ID")
    client_secret: str | None = Field(
        None, min_length=1, description="OAuth client secret"
    )
    authorize_url: HttpUrl | None = Field(
        None, description="OAuth authorization endpoint URL"
    )
    token_url: HttpUrl | None = Field(
        None, description="OAuth token exchange endpoint URL"
    )
    user_info_url: HttpUrl | None = Field(
        None, description="User information endpoint URL"
    )
    scope: str | None = Field(None, description="OAuth scope string")

    # User data mapping configuration
    user_id_field: str | None = Field(
        None, description="Field name for user ID in provider response"
    )
    email_field: str | None = Field(
        None, description="Field name for email in provider response"
    )
    name_field: str | None = Field(
        None, description="Field name for name in provider response"
    )
    username_field: str | None = Field(
        None, description="Field name for username in provider response"
    )
    avatar_field: str | None = Field(
        None, description="Field name for avatar URL in provider response"
    )


class OAuthProviderResponse(BaseModel):
    """Schema for OAuth provider configuration response."""

    id: UUID
    name: str
    display_name: str
    description: str | None
    is_enabled: bool
    icon_url: str | None

    # OAuth configuration (excluding sensitive data)
    client_id: str
    authorize_url: str
    token_url: str
    user_info_url: str
    scope: str

    # User data mapping configuration
    user_id_field: str
    email_field: str
    name_field: str
    username_field: str | None
    avatar_field: str | None

    created_at: str
    updated_at: str

    model_config = ConfigDict(from_attributes=True)


class OAuthProviderListResponse(BaseModel):
    """Schema for OAuth provider list response."""

    providers: list[OAuthProviderResponse]
    total: int


class OAuthProviderPublicInfo(BaseModel):
    """Schema for public OAuth provider information (for client-side display)."""

    name: str
    display_name: str
    description: str | None
    icon_url: str | None


class OAuthProviderTestRequest(BaseModel):
    """Schema for testing OAuth provider configuration."""

    test_code: str = Field(
        ..., description="Test authorization code to validate configuration"
    )


class OAuthProviderTestResponse(BaseModel):
    """Schema for OAuth provider test response."""

    success: bool
    message: str
    user_info: dict | None = Field(
        None, description="Sample user info if test successful"
    )


class OAuthProviderStatsResponse(BaseModel):
    """Schema for OAuth provider usage statistics."""

    provider_name: str
    total_users: int
    active_users: int
    last_used: str | None
