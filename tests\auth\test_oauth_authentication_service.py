"""Tests for OAuthAuthenticationService."""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock

import pytest

from modules.auth.application.credential_auth_service import CredentialAuthService
from modules.auth.application.oauth_authentication_service import (
    OAuthAuthenticationService,
)
from modules.auth.domain.credential_auth_repositories import UserRepository
from modules.auth.domain.oauth_account_models import OAuthAccount
from modules.auth.domain.oauth_account_repositories import OAuthAccountRepository
from modules.user.domain.user_models import User, UserStatus
from shared.oauth.client_service import OAuthClientService


@pytest.fixture
def mock_user_repository():
    """Create mock user repository."""
    return AsyncMock(spec=UserRepository)


@pytest.fixture
def mock_oauth_account_repository():
    """Create mock OAuth account repository."""
    return AsyncMock(spec=OAuthAccountRepository)


@pytest.fixture
def mock_oauth_client_service():
    """Create mock OAuth client service."""
    return AsyncMock(spec=OAuthClientService)


@pytest.fixture
def mock_credential_auth_service():
    """Create mock credential auth service."""
    return AsyncMock(spec=CredentialAuthService)


@pytest.fixture
def oauth_auth_service(
    mock_user_repository,
    mock_oauth_account_repository,
    mock_oauth_client_service,
    mock_credential_auth_service,
):
    """Create OAuthAuthenticationService instance for testing."""
    return OAuthAuthenticationService(
        user_repository=mock_user_repository,
        oauth_account_repository=mock_oauth_account_repository,
        oauth_client_service=mock_oauth_client_service,
        credential_auth_service=mock_credential_auth_service,
    )


@pytest.fixture
def sample_user():
    """Create sample user for testing."""
    return User(
        id=uuid.uuid4(),
        username="testuser",
        email="<EMAIL>",
        password_hash="$2b$12$hashed_password",
        status=UserStatus.ACTIVE,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def sample_oauth_account(sample_user):
    """Create sample OAuth account for testing."""
    return OAuthAccount(
        id=uuid.uuid4(),
        user_id=sample_user.id,
        provider="github",
        provider_account_id="12345",
        created_at=datetime.now(),
    )


class TestOAuthAuthenticationService:
    """Test cases for OAuthAuthenticationService."""

    @pytest.mark.asyncio
    async def should_get_available_providers_from_oauth_client_service(
        self, oauth_auth_service, mock_oauth_client_service
    ):
        """Test that available providers are retrieved from OAuth client service."""
        from types import SimpleNamespace

        mock_provider = SimpleNamespace(
            name="github",
            display_name="GitHub",
            description="GitHub OAuth provider",
            icon_url="https://github.com/favicon.ico",
        )
        mock_oauth_client_service.get_all_enabled_providers.return_value = [
            mock_provider
        ]

        result = await oauth_auth_service.get_available_providers()

        expected = [
            {
                "name": "github",
                "display_name": "GitHub",
                "description": "GitHub OAuth provider",
                "icon_url": "https://github.com/favicon.ico",
            }
        ]
        assert result == expected
        mock_oauth_client_service.get_all_enabled_providers.assert_called_once()

    @pytest.mark.asyncio
    async def should_get_authorization_url_from_oauth_client_service(
        self, oauth_auth_service, mock_oauth_client_service
    ):
        """Test that authorization URL is retrieved from OAuth client service."""
        mock_url = "https://github.com/login/oauth/authorize?client_id=test"
        mock_oauth_client_service.generate_authorization_url.return_value = mock_url

        result = await oauth_auth_service.get_authorization_url("github", "state123")

        assert result == mock_url
        mock_oauth_client_service.generate_authorization_url.assert_called_once_with(
            "github", "state123"
        )

    @pytest.mark.asyncio
    async def should_handle_oauth_callback_for_new_user(
        self,
        oauth_auth_service,
        mock_oauth_client_service,
        mock_user_repository,
        mock_oauth_account_repository,
        mock_credential_auth_service,
        sample_user,
    ):
        """Test OAuth callback handling for new user."""
        # Mock OAuth flow
        mock_user_info = {
            "oauth_id": "12345",
            "email": "<EMAIL>",
            "name": "Test User",
            "username": "testuser",
            "avatar_url": "https://avatar.url",
        }
        mock_oauth_client_service.handle_oauth_flow.return_value = {
            "access_token": "token",
            "user_info": mock_user_info,
        }

        # Mock user creation
        mock_user_repository.get_by_email.return_value = None
        mock_user_repository.create.return_value = sample_user
        mock_oauth_account_repository.get_by_provider_and_id.return_value = None
        mock_oauth_account_repository.create.return_value = None
        mock_credential_auth_service.create_access_token.return_value = "jwt_token"

        user, token = await oauth_auth_service.handle_oauth_callback(
            "github", "auth_code"
        )

        assert user.email == "<EMAIL>"
        assert token == "jwt_token"
        mock_user_repository.create.assert_called_once()
        mock_oauth_account_repository.create.assert_called_once()

    @pytest.mark.asyncio
    async def should_handle_oauth_callback_for_existing_user_with_oauth_account(
        self,
        oauth_auth_service,
        mock_oauth_client_service,
        mock_user_repository,
        mock_oauth_account_repository,
        mock_credential_auth_service,
        sample_user,
        sample_oauth_account,
    ):
        """Test OAuth callback handling for existing user with OAuth account."""
        # Mock OAuth flow
        mock_user_info = {
            "oauth_id": "12345",
            "email": "<EMAIL>",
            "name": "Test User",
            "username": "testuser",
            "avatar_url": "https://avatar.url",
        }
        mock_oauth_client_service.handle_oauth_flow.return_value = {
            "access_token": "token",
            "user_info": mock_user_info,
        }

        # Mock existing OAuth account
        mock_oauth_account_repository.get_by_provider_and_id.return_value = (
            sample_oauth_account
        )
        mock_user_repository.get_by_id.return_value = sample_user
        mock_credential_auth_service.create_access_token.return_value = "jwt_token"

        user, token = await oauth_auth_service.handle_oauth_callback(
            "github", "auth_code"
        )

        assert user.id == sample_user.id
        assert token == "jwt_token"
        mock_user_repository.create.assert_not_called()  # Should not create new user
        mock_oauth_account_repository.create.assert_not_called()  # Should not create new OAuth account

    @pytest.mark.asyncio
    async def should_handle_oauth_callback_for_existing_user_without_oauth_account(
        self,
        oauth_auth_service,
        mock_oauth_client_service,
        mock_user_repository,
        mock_oauth_account_repository,
        mock_credential_auth_service,
        sample_user,
    ):
        """Test OAuth callback handling for existing user without OAuth account."""
        # Mock OAuth flow
        mock_user_info = {
            "oauth_id": "12345",
            "email": "<EMAIL>",
            "name": "Test User",
            "username": "testuser",
            "avatar_url": "https://avatar.url",
        }
        mock_oauth_client_service.handle_oauth_flow.return_value = {
            "access_token": "token",
            "user_info": mock_user_info,
        }

        # Mock existing user but no OAuth account
        mock_oauth_account_repository.get_by_provider_and_id.return_value = None
        mock_user_repository.get_by_email.return_value = sample_user
        mock_oauth_account_repository.create.return_value = None
        mock_credential_auth_service.create_access_token.return_value = "jwt_token"

        user, token = await oauth_auth_service.handle_oauth_callback(
            "github", "auth_code"
        )

        assert user.id == sample_user.id
        assert token == "jwt_token"
        mock_user_repository.create.assert_not_called()  # Should not create new user
        mock_oauth_account_repository.create.assert_called_once()  # Should create OAuth account

    def should_get_user_oauth_accounts(
        self,
        oauth_auth_service,
        mock_oauth_account_repository,
        sample_user,
        sample_oauth_account,
    ):
        """Test getting user's OAuth accounts."""
        mock_oauth_account_repository.get_by_user_id.return_value = [
            sample_oauth_account
        ]

        result = oauth_auth_service.get_user_oauth_accounts(sample_user.id)

        assert len(result) == 1
        assert result[0].provider == "github"
        mock_oauth_account_repository.get_by_user_id.assert_called_once_with(
            sample_user.id
        )

    def should_unlink_oauth_account_when_account_belongs_to_user(
        self,
        oauth_auth_service,
        mock_oauth_account_repository,
        mock_user_repository,
        sample_user,
        sample_oauth_account,
    ):
        """Test unlinking OAuth account when it belongs to the user."""
        # Mock user with password (so they have alternative auth method)
        sample_user.hashed_password = "hashed_password"
        mock_user_repository.get_by_id.return_value = sample_user
        mock_oauth_account_repository.get_by_user_id.return_value = [
            sample_oauth_account
        ]
        mock_oauth_account_repository.delete.return_value = True

        result = oauth_auth_service.unlink_oauth_account(
            user_id=sample_user.id,
            oauth_account_id=sample_oauth_account.id,
        )

        assert result is True
        mock_oauth_account_repository.delete.assert_called_once_with(
            sample_oauth_account.id
        )

    def should_not_unlink_oauth_account_when_account_does_not_belong_to_user(
        self,
        oauth_auth_service,
        mock_oauth_account_repository,
        sample_oauth_account,
    ):
        """Test not unlinking OAuth account when it doesn't belong to the user."""
        different_user_id = uuid.uuid4()
        mock_oauth_account_repository.get_by_user_id.return_value = []  # No accounts for this user

        result = oauth_auth_service.unlink_oauth_account(
            user_id=different_user_id,
            oauth_account_id=sample_oauth_account.id,
        )

        assert result is False
        mock_oauth_account_repository.delete.assert_not_called()

    def should_not_unlink_oauth_account_when_account_not_found(
        self,
        oauth_auth_service,
        mock_oauth_account_repository,
    ):
        """Test not unlinking OAuth account when account is not found."""
        mock_oauth_account_repository.get_by_user_id.return_value = []  # No accounts for this user

        result = oauth_auth_service.unlink_oauth_account(
            user_id=uuid.uuid4(),
            oauth_account_id=uuid.uuid4(),
        )

        assert result is False
        mock_oauth_account_repository.delete.assert_not_called()

    @pytest.mark.asyncio
    async def should_raise_error_when_oauth_code_exchange_fails(
        self,
        oauth_auth_service,
        mock_oauth_client_service,
    ):
        """Test that error is raised when OAuth code exchange fails."""
        mock_oauth_client_service.handle_oauth_flow.side_effect = ValueError(
            "Invalid code"
        )

        with pytest.raises(ValueError, match="Invalid code"):
            await oauth_auth_service.handle_oauth_callback("github", "invalid_code")

    @pytest.mark.asyncio
    async def should_raise_error_when_user_info_fetch_fails(
        self,
        oauth_auth_service,
        mock_oauth_client_service,
    ):
        """Test that error is raised when user info fetch fails."""
        mock_oauth_client_service.handle_oauth_flow.side_effect = ValueError(
            "Failed to fetch user info"
        )

        with pytest.raises(ValueError, match="Failed to fetch user info"):
            await oauth_auth_service.handle_oauth_callback("github", "auth_code")
