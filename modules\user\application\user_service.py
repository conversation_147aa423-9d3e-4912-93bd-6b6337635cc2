from uuid import UUID

from modules.user.domain.user_models import User, UserStatus
from modules.user.domain.user_repositories import UserRepository


class UserService:
    def __init__(self, user_repository: UserRepository):
        self.user_repository = user_repository

    def get_user_by_id(self, user_id: UUID) -> User | None:
        return self.user_repository.get_by_id(user_id)

    def get_user_by_username(self, username: str) -> User | None:
        return self.user_repository.get_by_username(username)

    def get_user_by_email(self, email: str) -> User | None:
        return self.user_repository.get_by_email(email)

    def get_user_by_username_or_email(self, username_or_email: str) -> User | None:
        user = self.get_user_by_username(username_or_email)
        if user:
            return user
        return self.get_user_by_email(username_or_email)

    def create_user(self, user: User) -> User:
        return self.user_repository.save(user)

    def update_user(self, user: User) -> User:
        return self.user_repository.save(user)

    def delete_user(self, user_id: UUID) -> bool:
        return self.user_repository.delete(user_id)

    def update_user_status(self, user_id: UUID, status: UserStatus) -> bool:
        return self.user_repository.update_status(user_id, status)

    def list_users(
        self, skip: int = 0, limit: int = 100, status: UserStatus | None = None
    ) -> list[User]:
        return self.user_repository.list_users(skip, limit, status)

    def search_users(self, query: str, skip: int = 0, limit: int = 100) -> list[User]:
        return self.user_repository.search_users(query, skip, limit)
