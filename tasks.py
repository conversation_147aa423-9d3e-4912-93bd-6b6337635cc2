"""
AI4SE MCP Hub - Development Tasks

This module provides development tasks using Invoke for code quality, testing, and optimization.
Run `inv --list` to see all available tasks.

Usage:
    inv format          # Format code
    inv lint            # Lint code
    inv test            # Run tests
    inv optimize        # Complete optimization
    inv --help          # Show help
"""

import os
import sys
from pathlib import Path

from invoke import task


@task
def help(c):
    """Show detailed help for all tasks"""
    print("AI4SE MCP Hub - Development Tasks")
    print("=" * 50)
    print()
    print("Setup Commands:")
    print("  inv install-dev     Install development dependencies")
    print()
    print("Code Quality Commands:")
    print("  inv format          Format code with autoflake, isort, and ruff")
    print("  inv lint            Lint code with ruff")
    print("  inv check           Check code without making changes")
    print("  inv type-check      Run type checking with mypy")
    print("  inv optimize        Run complete code optimization")
    print()
    print("Testing Commands:")
    print("  inv test            Run all tests")
    print("  inv test-cov        Run tests with coverage report")
    print()
    print("Database Commands:")
    print("  inv db-upgrade      Run database migrations")
    print("  inv db-downgrade    Downgrade database by one revision")
    print()
    print("Utility Commands:")
    print("  inv clean           Clean cache and temporary files (project scope only)")
    print("  inv clean --deep    Deep clean including additional project files")
    print("  inv server          Start development server")
    print()
    print("Workflow Commands:")
    print("  inv pre-commit      Simulate pre-commit checks")
    print("  inv ci              Run CI checks")
    print()


@task
def install_dev(c):
    """Install development dependencies"""
    print("[INSTALL] Installing development dependencies...")
    c.run("uv add --dev ruff black isort autoflake mypy invoke")
    print("[SUCCESS] Development dependencies installed")


@task
def format(c):
    """Format code with autoflake, isort, and ruff"""
    print("[FORMAT] Formatting code...")
    c.run(
        "autoflake --remove-all-unused-imports --remove-unused-variables --in-place --recursive --exclude=.uv_cache ."
    )
    c.run("isort .")
    c.run("ruff format .")
    print("[SUCCESS] Code formatting completed")


@task
def lint(c):
    """Lint code with ruff"""
    print("[LINT] Linting code...")
    c.run("ruff check --fix .")
    print("[SUCCESS] Linting completed")


@task
def check(c):
    """Check code without making changes"""
    print("[CHECK] Checking code quality...")
    c.run(
        "autoflake --remove-all-unused-imports --remove-unused-variables --recursive --exclude=.uv_cache ."
    )
    c.run("isort --check-only --diff .")
    c.run("ruff format --check .")
    c.run("ruff check .")
    print("[SUCCESS] Code quality check completed")


@task
def type_check(c):
    """Run type checking with mypy"""
    print("[TYPE] Running type checks...")
    c.run("mypy modules/ common/ tests/", warn=True)
    print("[SUCCESS] Type checking completed")


@task
def optimize(c):
    """Run complete code optimization"""
    print("[OPTIMIZE] Running complete code optimization...")
    c.run("python scripts/code_optimize.py --skip-types")
    print("[SUCCESS] Code optimization completed")


@task
def optimize_check(c):
    """Check code optimization without making changes"""
    print("[CHECK] Checking code optimization...")
    c.run("python scripts/code_optimize.py --check-only --skip-types")
    print("[SUCCESS] Code optimization check completed")


@task
def test(c):
    """Run all tests"""
    print("[TEST] Running tests...")
    c.run("pytest")
    print("[SUCCESS] Tests completed")


@task
def test_cov(c):
    """Run tests with coverage report"""
    print("[TEST] Running tests with coverage...")
    c.run("pytest --cov=modules --cov-report=html --cov-report=term")
    print("[SUCCESS] Tests with coverage completed")
    print("[INFO] Coverage report generated in htmlcov/")


@task
def db_upgrade(c):
    """Run database migrations"""
    print("[DB] Running database migrations...")
    c.run("alembic upgrade head")
    print("[SUCCESS] Database migrations completed")


@task
def db_downgrade(c):
    """Downgrade database by one revision"""
    print("[DB] Downgrading database...")
    c.run("alembic downgrade -1")
    print("[SUCCESS] Database downgrade completed")


@task
def clean(c, deep=False):
    """Clean cache and temporary files within project scope only

    Only cleans files and directories within the project directory.
    Reads uv cache configuration from pyproject.toml and only cleans if configured.

    Args:
        deep: If True, perform deep cleaning of additional project files (.DS_Store, Thumbs.db, etc.)
    """
    print("[CLEAN] Cleaning cache and temporary files...")

    import shutil
    import tomllib

    # Read uv cache configuration from pyproject.toml
    uv_cache_dir = None
    try:
        with open("pyproject.toml", "rb") as f:
            pyproject_data = tomllib.load(f)
            uv_cache_dir = pyproject_data.get("tool", {}).get("uv", {}).get("cache-dir")
    except (FileNotFoundError, tomllib.TOMLDecodeError) as e:
        print(f"  Warning: Could not read pyproject.toml: {e}")

    # Define patterns to clean
    patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo",
        "*.egg-info",
        ".pytest_cache",
        ".coverage",
        "htmlcov",
        ".mypy_cache",
        ".ruff_cache",
    ]

    # Note: uv cache will be handled separately to avoid duplication
    if uv_cache_dir:
        print(f"  Found uv cache configuration: {uv_cache_dir}")

    cleaned_count = 0
    excluded_dirs = {".venv", ".git"}

    # Clean files and directories using Python (exclude .venv and .git)
    for pattern in patterns:
        if pattern.startswith("*"):
            # File patterns - use glob to find files
            for file_path in Path(".").rglob(pattern):
                # Skip files in excluded directories
                if any(
                    excluded_dir in str(file_path) for excluded_dir in excluded_dirs
                ):
                    continue
                try:
                    file_path.unlink()
                    cleaned_count += 1
                    print(f"  Removed file: {file_path}")
                except (OSError, PermissionError) as e:
                    print(f"  Warning: Could not remove {file_path}: {e}")
        else:
            # Directory patterns - use glob to find directories
            for dir_path in Path(".").rglob(pattern):
                # Skip directories in excluded directories
                if any(excluded_dir in str(dir_path) for excluded_dir in excluded_dirs):
                    continue
                if dir_path.is_dir():
                    try:
                        shutil.rmtree(dir_path)
                        cleaned_count += 1
                        print(f"  Removed directory: {dir_path}")
                    except (OSError, PermissionError) as e:
                        print(f"  Warning: Could not remove {dir_path}: {e}")

    # Clean project-configured uv cache only if it exists and is within project
    if uv_cache_dir:
        uv_cache_path = Path(uv_cache_dir)
        # Only clean if it's a relative path (within project directory)
        if not uv_cache_path.is_absolute():
            if uv_cache_path.exists():
                try:
                    if uv_cache_path.is_dir():
                        shutil.rmtree(uv_cache_path)
                        cleaned_count += 1
                        print(f"  Removed project uv cache: {uv_cache_path}")
                    else:
                        print(
                            f"  Warning: uv cache path is not a directory: {uv_cache_path}"
                        )
                except (OSError, PermissionError) as e:
                    print(f"  Warning: Could not remove uv cache {uv_cache_path}: {e}")
            else:
                print(f"  uv cache directory not found: {uv_cache_path}")
        else:
            print(
                f"  Skipping absolute uv cache path (outside project): {uv_cache_path}"
            )
    else:
        print("  No uv cache configuration found in pyproject.toml")

    # Deep cleaning (optional) - only project-scope files
    if deep:
        print("[CLEAN] Performing deep cleaning (project scope only)...")
        deep_cleaned = 0

        # Clean additional project-specific files
        additional_patterns = [
            ".DS_Store",  # macOS
            "Thumbs.db",  # Windows
            "*.tmp",
            "*.temp",
            ".coverage.*",
        ]

        for pattern in additional_patterns:
            if pattern.startswith("*") or pattern.startswith("."):
                for file_path in Path(".").rglob(pattern):
                    if any(
                        excluded_dir in str(file_path) for excluded_dir in excluded_dirs
                    ):
                        continue
                    try:
                        if file_path.is_file():
                            file_path.unlink()
                            deep_cleaned += 1
                            print(f"  Removed system file: {file_path}")
                    except (OSError, PermissionError) as e:
                        print(f"  Warning: Could not remove {file_path}: {e}")

        print(
            f"[SUCCESS] Deep cleaning completed - removed {deep_cleaned} additional items"
        )

    print(f"[SUCCESS] Cleanup completed - removed {cleaned_count} items total")


@task
def server(c, host="127.0.0.1", port=8000, reload=True):
    """Start development server"""
    print(f"[SERVER] Starting development server on http://{host}:{port}")
    reload_flag = "--reload" if reload else ""
    c.run(f"uvicorn main:app --host {host} --port {port} {reload_flag}")


@task(format, lint, type_check, test)
def pre_commit(c):
    """Simulate pre-commit checks"""
    print("[SUCCESS] Pre-commit checks completed successfully")


@task(check, type_check, test)
def ci(c):
    """Run CI checks"""
    print("[SUCCESS] CI checks completed successfully")


# Additional utility tasks
@task
def deps_update(c):
    """Update all dependencies"""
    print("[DEPS] Updating dependencies...")
    c.run("uv sync --upgrade")
    print("[SUCCESS] Dependencies updated")


@task
def deps_audit(c):
    """Audit dependencies for security issues"""
    print("[AUDIT] Auditing dependencies...")
    c.run("uv audit", warn=True)
    print("[SUCCESS] Dependency audit completed")


@task
def info(c):
    """Show project information"""
    print("AI4SE MCP Hub - Project Information")
    print("=" * 40)
    print(f"Python version: {sys.version}")
    print(f"Project root: {Path.cwd()}")
    print(f"Virtual env: {os.getenv('VIRTUAL_ENV', 'Not activated')}")

    # Check if key files exist
    key_files = ["pyproject.toml", "requirements.txt", ".env", "alembic.ini"]
    print("\nKey files:")
    for file in key_files:
        status = "[OK]" if Path(file).exists() else "[MISSING]"
        print(f"  {status} {file}")

    print("\nAvailable tasks: run `inv --list` to see all tasks")
