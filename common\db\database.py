import os
from collections.abc import Generator

from sqlalchemy import create_engine
from sqlalchemy.orm import Session, declarative_base, sessionmaker

# --- Database URL ---
# In a real app, use env variables and a proper config system
DATABASE_URL = os.getenv("AI4SE_MCP_HUB_DB_URL", "sqlite:///./ai4se-mcp-hub.db")

# --- SQLAlchemy Engine (Sync) ---
connect_args = {}
if DATABASE_URL.startswith("sqlite"):
    connect_args = {"check_same_thread": False}

engine = create_engine(DATABASE_URL, connect_args=connect_args)

# --- SQLAlchemy Session (Sync) ---
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# --- Base for ORM models ---
Base = declarative_base()


# --- Dependency functions ---
def get_db() -> Generator[Session, None, None]:
    """Get synchronous database session."""
    db = SessionLocal()
    try:
        yield db
        db.commit()
    except Exception:
        db.rollback()
        raise
    finally:
        db.close()
