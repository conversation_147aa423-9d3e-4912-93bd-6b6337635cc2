"""Credential authentication service for username/password authentication."""

import uuid
from datetime import UTC, datetime, timedelta

import jwt
from passlib.context import CryptContext
from pydantic import EmailStr

from modules.auth.domain.credential_auth_repositories import UserRepository
from modules.user.domain.user_models import User, UserStatus


class CredentialAuthService:
    """Service for handling username/password authentication."""

    def __init__(
        self,
        user_repository: UserRepository,
        secret_key: str,
        algorithm: str = "HS256",
        token_expire_minutes: int = 30,
    ):
        self.user_repository = user_repository
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.token_expire_minutes = token_expire_minutes
        # Configure passlib to suppress bcrypt version warnings
        self.pwd_context = CryptContext(
            schemes=["bcrypt"], deprecated="auto", bcrypt__default_rounds=12
        )

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)

    def get_password_hash(self, password: str) -> str:
        """Hash a password."""
        return self.pwd_context.hash(password)

    def create_access_token(self, user: User) -> str:
        """Create JWT access token for user."""
        expire = datetime.now(UTC) + timedelta(minutes=self.token_expire_minutes)
        to_encode = {
            "sub": str(user.id),
            "username": user.username,
            "email": user.email,
            "exp": expire,
        }
        encoded_jwt = jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
        return encoded_jwt

    def verify_token(self, token: str) -> dict | None:
        """Verify and decode JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.PyJWTError:
            return None

    def register_user(self, username: str, email: EmailStr, password: str) -> User:
        """Register a new user with username, email, and password."""
        # Check if username already exists
        if self.user_repository.exists_by_username(username):
            raise ValueError(f"Username '{username}' already exists")

        # Check if email already exists
        if self.user_repository.exists_by_email(email):
            raise ValueError(f"Email '{email}' already exists")

        # Create new user
        now = datetime.now()
        user = User(
            id=uuid.uuid4(),
            username=username,
            email=email,
            hashed_password=self.get_password_hash(password),
            status=UserStatus.ACTIVE,
            created_at=now,
            updated_at=now,
        )

        return self.user_repository.create(user)

    def authenticate_user(self, username_or_email: str, password: str) -> User | None:
        """Authenticate user with username/email and password."""
        user = self.user_repository.get_by_username_or_email(username_or_email)

        if not user:
            return None

        if not user.hashed_password:
            return None

        if not self.verify_password(password, user.hashed_password):
            return None

        # Check if user is active
        if user.status != UserStatus.ACTIVE:
            raise ValueError(f"User account is {user.status.value}")

        return user

    def login(self, username_or_email: str, password: str) -> tuple[User, str]:
        """Login user and return user with access token."""
        user = self.authenticate_user(username_or_email, password)

        if not user:
            raise ValueError("Invalid username/email or password")

        access_token = self.create_access_token(user)
        return user, access_token

    def get_user_by_id(self, user_id: str) -> User | None:
        """Get user by ID from token payload."""
        try:
            user_uuid = uuid.UUID(user_id)
            return self.user_repository.get_by_id(user_uuid)
        except (ValueError, TypeError):
            return None

    def change_password(
        self, user_id: uuid.UUID, current_password: str, new_password: str
    ) -> bool:
        """Change user password."""
        user = self.user_repository.get_by_id(user_id)

        if not user or not user.hashed_password:
            return False

        # Verify current password
        if not self.verify_password(current_password, user.hashed_password):
            return False

        # Update password
        user.hashed_password = self.get_password_hash(new_password)
        user.updated_at = datetime.now()

        self.user_repository.update(user)
        return True

    def update_user_status(self, user_id: uuid.UUID, status: UserStatus) -> bool:
        """Update user status."""
        return self.user_repository.update_status(user_id, status)

    def deactivate_user(self, user_id: uuid.UUID) -> bool:
        """Deactivate user account."""
        return self.update_user_status(user_id, UserStatus.INACTIVE)

    def suspend_user(self, user_id: uuid.UUID) -> bool:
        """Suspend user account."""
        return self.update_user_status(user_id, UserStatus.SUSPENDED)

    def reactivate_user(self, user_id: uuid.UUID) -> bool:
        """Reactivate user account."""
        return self.update_user_status(user_id, UserStatus.ACTIVE)
