"""OAuth authentication API schemas."""

from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field


class OAuthProviderInfo(BaseModel):
    """Schema for OAuth provider information."""

    name: str
    display_name: str
    description: str | None
    icon_url: str | None


class OAuthProvidersResponse(BaseModel):
    """Schema for OAuth providers list response."""

    providers: list[OAuthProviderInfo]


class OAuthAuthorizationResponse(BaseModel):
    """Schema for OAuth authorization URL response."""

    authorization_url: str
    state: str | None = None


class OAuthCallbackRequest(BaseModel):
    """Schema for OAuth callback request."""

    code: str = Field(..., description="Authorization code from OAuth provider")
    state: str | None = Field(None, description="State parameter for CSRF protection")


class OAuthAccountResponse(BaseModel):
    """Schema for OAuth account response."""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    provider: str
    provider_account_id: str
    created_at: str


class UserOAuthAccountsResponse(BaseModel):
    """Schema for user's OAuth accounts response."""

    oauth_accounts: list[OAuthAccountResponse]
