#!/usr/bin/env python3
"""
Code optimization script for AI4SE MCP Hub project.

This script performs comprehensive code optimization including:
- Code formatting with Black and Ruff
- Import sorting with isort
- Unused import removal with autoflake
- Type checking with mypy
- Linting with ruff

Usage:
    python scripts/code_optimize.py [--check-only] [--skip-types]
"""

import argparse
import subprocess
import sys
from pathlib import Path


class CodeOptimizer:
    """Code optimization utility for the project."""

    def __init__(
        self, project_root: Path, check_only: bool = False, skip_types: bool = False
    ):
        self.project_root = project_root
        self.check_only = check_only
        self.skip_types = skip_types
        self.errors: list[str] = []

    def run_command(self, cmd: list[str], description: str) -> tuple[bool, str]:
        """Run a command and return success status and output."""
        print(f"[RUNNING] {description}...")
        try:
            result = subprocess.run(
                cmd, cwd=self.project_root, capture_output=True, text=True, check=False
            )

            if result.returncode == 0:
                print(f"[SUCCESS] {description} completed successfully")
                return True, result.stdout
            else:
                error_msg = f"[ERROR] {description} failed:\n{result.stderr}"
                print(error_msg)
                self.errors.append(error_msg)
                return False, result.stderr

        except FileNotFoundError:
            error_msg = f"[ERROR] Command not found: {cmd[0]}"
            print(error_msg)
            self.errors.append(error_msg)
            return False, error_msg

    def remove_unused_imports(self) -> bool:
        """Remove unused imports and variables with autoflake."""
        if self.check_only:
            cmd = [
                "autoflake",
                "--remove-all-unused-imports",
                "--remove-unused-variables",
                "--recursive",
                ".",
            ]
            description = "Checking for unused imports"
        else:
            cmd = [
                "autoflake",
                "--remove-all-unused-imports",
                "--remove-unused-variables",
                "--in-place",
                "--recursive",
                ".",
            ]
            description = "Removing unused imports and variables"

        return self.run_command(cmd, description)[0]

    def sort_imports(self) -> bool:
        """Sort imports with isort."""
        if self.check_only:
            cmd = ["isort", "--check-only", "--diff", "."]
            description = "Checking import sorting"
        else:
            cmd = ["isort", "."]
            description = "Sorting imports"

        return self.run_command(cmd, description)[0]

    def format_with_black(self) -> bool:
        """Format code with Black."""
        if self.check_only:
            cmd = ["black", "--check", "--diff", "."]
            description = "Checking code formatting (Black)"
        else:
            cmd = ["black", "."]
            description = "Formatting code with Black"

        return self.run_command(cmd, description)[0]

    def format_with_ruff(self) -> bool:
        """Format code with Ruff."""
        if self.check_only:
            cmd = ["ruff", "format", "--check", "."]
            description = "Checking code formatting (Ruff)"
        else:
            cmd = ["ruff", "format", "."]
            description = "Formatting code with Ruff"

        return self.run_command(cmd, description)[0]

    def lint_with_ruff(self) -> bool:
        """Lint code with Ruff."""
        if self.check_only:
            cmd = ["ruff", "check", "."]
            description = "Linting code with Ruff"
        else:
            cmd = ["ruff", "check", "--fix", "."]
            description = "Linting and fixing code with Ruff"

        return self.run_command(cmd, description)[0]

    def type_check(self) -> bool:
        """Run type checking with mypy."""
        if self.skip_types:
            print("[SKIP] Skipping type checking")
            return True

        # Run mypy on individual modules to avoid project name issues
        modules_to_check = ["modules", "common", "tests"]
        all_success = True

        for module in modules_to_check:
            cmd = ["mypy", module]
            description = f"Type checking {module} with mypy"
            success, _ = self.run_command(cmd, description)
            if not success:
                all_success = False

        return all_success

    def optimize(self) -> bool:
        """Run all optimization steps."""
        print("[START] Starting code optimization...")
        print(f"[INFO] Project root: {self.project_root}")
        print(f"[INFO] Check only mode: {self.check_only}")
        print(f"[INFO] Skip type checking: {self.skip_types}")
        print("-" * 50)

        steps = [
            ("Remove unused imports", self.remove_unused_imports),
            ("Sort imports", self.sort_imports),
            ("Format with Ruff", self.format_with_ruff),
            ("Lint with Ruff", self.lint_with_ruff),
            ("Type check", self.type_check),
        ]

        success_count = 0
        for _step_name, step_func in steps:
            if step_func():
                success_count += 1

        print("-" * 50)
        print(f"[SUMMARY] Completed {success_count}/{len(steps)} steps successfully")

        if self.errors:
            print("\n[ERROR] Errors encountered:")
            for error in self.errors:
                print(f"  - {error}")
            return False
        else:
            print("[SUCCESS] All optimization steps completed successfully!")
            return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Optimize code quality for AI4SE MCP Hub"
    )
    parser.add_argument(
        "--check-only",
        action="store_true",
        help="Only check for issues without making changes",
    )
    parser.add_argument(
        "--skip-types", action="store_true", help="Skip type checking with mypy"
    )

    args = parser.parse_args()

    # Find project root
    script_dir = Path(__file__).parent
    project_root = script_dir.parent

    # Ensure we're in the right directory
    if not (project_root / "pyproject.toml").exists():
        print("[ERROR] Could not find pyproject.toml. Are you in the project root?")
        sys.exit(1)

    # Run optimization
    optimizer = CodeOptimizer(
        project_root=project_root,
        check_only=args.check_only,
        skip_types=args.skip_types,
    )

    success = optimizer.optimize()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
