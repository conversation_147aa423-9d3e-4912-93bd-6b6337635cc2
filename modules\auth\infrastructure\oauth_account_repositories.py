"""OAuth account repository implementations."""

from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from modules.auth.domain.oauth_account_models import OAuthAccount
from modules.auth.domain.oauth_account_repositories import OAuthAccountRepository
from modules.auth.infrastructure.oauth_account_orm import OAuthAccountORM


class OAuthAccountRepositoryImpl(OAuthAccountRepository):
    """SQLAlchemy implementation of OAuth account repository."""

    def __init__(self, session: Session):
        self.session = session

    def create(self, oauth_account: OAuthAccount) -> OAuthAccount:
        """Create a new OAuth account."""
        oauth_orm = OAuthAccountORM(
            id=oauth_account.id,
            user_id=oauth_account.user_id,
            provider=oauth_account.provider,
            provider_account_id=oauth_account.provider_account_id,
            created_at=oauth_account.created_at,
        )

        self.session.add(oauth_orm)
        self.session.flush()
        self.session.refresh(oauth_orm)

        return self._orm_to_domain(oauth_orm)

    def get_by_provider_and_id(
        self, provider: str, provider_account_id: str
    ) -> OAuthAccount | None:
        """Get OAuth account by provider and provider account ID."""
        stmt = select(OAuthAccountORM).where(
            OAuthAccountORM.provider == provider,
            OAuthAccountORM.provider_account_id == provider_account_id,
        )
        result = self.session.execute(stmt)
        oauth_orm = result.scalar_one_or_none()

        return self._orm_to_domain(oauth_orm) if oauth_orm else None

    def get_by_user_id(self, user_id: UUID) -> list[OAuthAccount]:
        """Get all OAuth accounts for a user."""
        stmt = select(OAuthAccountORM).where(OAuthAccountORM.user_id == user_id)
        result = self.session.execute(stmt)
        oauth_accounts_orm = result.scalars().all()

        return [self._orm_to_domain(orm) for orm in oauth_accounts_orm]

    def delete(self, oauth_account_id: UUID) -> bool:
        """Delete an OAuth account."""
        stmt = select(OAuthAccountORM).where(OAuthAccountORM.id == oauth_account_id)
        result = self.session.execute(stmt)
        oauth_orm = result.scalar_one_or_none()

        if not oauth_orm:
            return False

        self.session.delete(oauth_orm)
        self.session.flush()

        return True

    def _orm_to_domain(self, oauth_orm: OAuthAccountORM) -> OAuthAccount:
        """Convert ORM object to domain model."""
        return OAuthAccount(
            id=oauth_orm.id,
            user_id=oauth_orm.user_id,
            provider=oauth_orm.provider,
            provider_account_id=oauth_orm.provider_account_id,
            created_at=oauth_orm.created_at,
        )
