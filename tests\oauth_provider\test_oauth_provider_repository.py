"""Tests for OAuth Provider Repository."""

import uuid
from datetime import datetime

import pytest

from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig
from modules.oauth_provider.infrastructure.oauth_provider_repositories import (
    OAuthProviderRepositoryImpl,
)


@pytest.fixture
def github_provider_config():
    """Create GitHub provider configuration for testing."""
    return OAuthProviderConfig(
        id=uuid.uuid4(),
        name="github",
        display_name="GitHub",
        description="GitHub OAuth provider",
        is_enabled=True,
        icon_url="https://github.com/favicon.ico",
        client_id="test_github_client_id",
        client_secret="test_github_client_secret",
        authorize_url="https://github.com/login/oauth/authorize",
        token_url="https://github.com/login/oauth/access_token",
        user_info_url="https://api.github.com/user",
        scope="user:email",
        user_id_field="id",
        email_field="email",
        name_field="name",
        username_field="login",
        avatar_field="avatar_url",
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def google_provider_config():
    """Create Google provider configuration for testing."""
    return OAuthProviderConfig(
        id=uuid.uuid4(),
        name="google",
        display_name="Google",
        description="Google OAuth provider",
        is_enabled=True,
        icon_url="https://google.com/favicon.ico",
        client_id="test_google_client_id",
        client_secret="test_google_client_secret",
        authorize_url="https://accounts.google.com/o/oauth2/v2/auth",
        token_url="https://oauth2.googleapis.com/token",
        user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
        scope="openid email profile",
        user_id_field="id",
        email_field="email",
        name_field="name",
        username_field=None,
        avatar_field="picture",
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


class TestOAuthProviderRepository:
    """Test cases for OAuth Provider Repository."""

    def should_create_oauth_provider_when_valid_config_given(
        self, db_session, github_provider_config: OAuthProviderConfig
    ):
        """Test that OAuth provider is created successfully."""
        repo = OAuthProviderRepositoryImpl(db_session)

        result = repo.create(github_provider_config)

        assert result.id == github_provider_config.id
        assert result.name == github_provider_config.name
        assert result.display_name == github_provider_config.display_name
        assert result.is_enabled == github_provider_config.is_enabled

    def should_get_oauth_provider_by_name_when_exists(
        self, db_session, github_provider_config: OAuthProviderConfig
    ):
        """Test that OAuth provider is retrieved by name successfully."""
        repo = OAuthProviderRepositoryImpl(db_session)

        # Create provider first
        repo.create(github_provider_config)
        db_session.commit()

        # Retrieve by name
        result = repo.get_by_name("github")

        assert result is not None
        assert result.name == "github"
        assert result.display_name == "GitHub"

    def should_return_none_when_provider_not_found_by_name(self, db_session):
        """Test that None is returned when provider is not found by name."""
        repo = OAuthProviderRepositoryImpl(db_session)

        result = repo.get_by_name("nonexistent")

        assert result is None

    def should_get_oauth_provider_by_id_when_exists(
        self, db_session, github_provider_config: OAuthProviderConfig
    ):
        """Test that OAuth provider is retrieved by ID successfully."""
        repo = OAuthProviderRepositoryImpl(db_session)

        # Create provider first
        created = repo.create(github_provider_config)
        db_session.commit()

        # Retrieve by ID
        result = repo.get_by_id(created.id)

        assert result is not None
        assert result.id == created.id
        assert result.name == "github"

    def should_get_all_enabled_providers_when_multiple_exist(
        self,
        db_session,
        github_provider_config: OAuthProviderConfig,
        google_provider_config: OAuthProviderConfig,
    ):
        """Test that all enabled providers are retrieved successfully."""
        repo = OAuthProviderRepositoryImpl(db_session)

        # Create providers
        repo.create(github_provider_config)
        repo.create(google_provider_config)

        # Create disabled provider
        disabled_config = OAuthProviderConfig(
            id=uuid.uuid4(),
            name="disabled",
            display_name="Disabled Provider",
            description="Disabled OAuth provider",
            is_enabled=False,
            icon_url=None,
            client_id="disabled_client_id",
            client_secret="disabled_client_secret",
            authorize_url="https://disabled.com/auth",
            token_url="https://disabled.com/token",
            user_info_url="https://disabled.com/user",
            scope="read",
            user_id_field="id",
            email_field="email",
            name_field="name",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        repo.create(disabled_config)
        db_session.commit()

        # Get all enabled providers
        result = repo.get_all_enabled()

        assert len(result) == 2
        provider_names = [p.name for p in result]
        assert "github" in provider_names
        assert "google" in provider_names
        assert "disabled" not in provider_names

    def should_get_all_providers_including_disabled(
        self, db_session, github_provider_config: OAuthProviderConfig
    ):
        """Test that all providers including disabled ones are retrieved."""
        repo = OAuthProviderRepositoryImpl(db_session)

        # Create enabled provider
        repo.create(github_provider_config)

        # Create disabled provider
        disabled_config = OAuthProviderConfig(
            id=uuid.uuid4(),
            name="disabled",
            display_name="Disabled Provider",
            description="Disabled OAuth provider",
            is_enabled=False,
            icon_url=None,
            client_id="disabled_client_id",
            client_secret="disabled_client_secret",
            authorize_url="https://disabled.com/auth",
            token_url="https://disabled.com/token",
            user_info_url="https://disabled.com/user",
            scope="read",
            user_id_field="id",
            email_field="email",
            name_field="name",
            created_at=datetime.now(),
            updated_at=datetime.now(),
        )
        repo.create(disabled_config)
        db_session.commit()

        # Get all providers
        result = repo.get_all()

        assert len(result) == 2
        provider_names = [p.name for p in result]
        assert "github" in provider_names
        assert "disabled" in provider_names

    def should_update_oauth_provider_when_valid_config_given(
        self, db_session, github_provider_config: OAuthProviderConfig
    ):
        """Test that OAuth provider is updated successfully."""
        repo = OAuthProviderRepositoryImpl(db_session)

        # Create provider first
        created = repo.create(github_provider_config)
        db_session.commit()

        # Update provider
        created.display_name = "Updated GitHub"
        created.description = "Updated description"
        created.is_enabled = False

        result = repo.update(created)
        db_session.commit()

        assert result.display_name == "Updated GitHub"
        assert result.description == "Updated description"
        assert result.is_enabled is False

    def should_delete_oauth_provider_when_exists(
        self, db_session, github_provider_config: OAuthProviderConfig
    ):
        """Test that OAuth provider is deleted successfully."""
        repo = OAuthProviderRepositoryImpl(db_session)

        # Create provider first
        created = repo.create(github_provider_config)
        db_session.commit()

        # Delete provider
        result = repo.delete(created.id)
        db_session.commit()

        assert result is True

        # Verify deletion
        deleted_provider = repo.get_by_id(created.id)
        assert deleted_provider is None

    def should_return_false_when_deleting_nonexistent_provider(self, db_session):
        """Test that False is returned when trying to delete nonexistent provider."""
        repo = OAuthProviderRepositoryImpl(db_session)

        result = repo.delete(uuid.uuid4())

        assert result is False

    def should_check_provider_existence_by_name(
        self, db_session, github_provider_config: OAuthProviderConfig
    ):
        """Test that provider existence is checked correctly by name."""
        repo = OAuthProviderRepositoryImpl(db_session)

        # Check non-existent provider
        exists_before = repo.exists_by_name("github")
        assert exists_before is False

        # Create provider
        repo.create(github_provider_config)
        db_session.commit()

        # Check existing provider
        exists_after = repo.exists_by_name("github")
        assert exists_after is True
