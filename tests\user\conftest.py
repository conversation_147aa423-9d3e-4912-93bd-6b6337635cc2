from collections.abc import Generator

import pytest
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session

from common.db.database import SessionLocal
from main import app
from modules.user.domain.user_models import UserStatus
from modules.user.infrastructure.user_orm import UserORM


@pytest.fixture(scope="module")
def test_client() -> TestClient:
    """Create a test client for user module tests"""
    return TestClient(app)


@pytest.fixture
def db() -> Generator[Session, None, None]:
    """Create a database session for user module tests"""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def test_user(db: Session) -> Generator[UserORM, None, None]:
    """Create a test user for individual test cases"""
    import uuid

    unique_suffix = str(uuid.uuid4())[:8]
    user = UserORM(
        username=f"testuser_api_{unique_suffix}",
        email=f"testuser_api_{unique_suffix}@example.com",
        hashed_password="hashed_password",  # noqa: S106
        status=UserStatus.ACTIVE,
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    yield user
    db.delete(user)
    db.commit()
