"""Tests for CredentialAuthService."""

import uuid
from datetime import datetime
from unittest.mock import Mock

import pytest

from modules.auth.application.credential_auth_service import CredentialAuthService
from modules.auth.domain.credential_auth_repositories import UserRepository
from modules.user.domain.user_models import User, UserStatus


@pytest.fixture
def mock_user_repository():
    """Create mock user repository."""
    return Mock(spec=UserRepository)


@pytest.fixture
def credential_auth_service(mock_user_repository):
    """Create CredentialAuthService instance for testing."""
    return CredentialAuthService(
        user_repository=mock_user_repository,
        secret_key="test_secret_key",
        algorithm="HS256",
        token_expire_minutes=30,
    )


@pytest.fixture
def sample_user():
    """Create sample user for testing."""
    return User(
        id=uuid.uuid4(),
        username="testuser",
        email="<EMAIL>",
        hashed_password="$2b$12$hashed_password",
        status=UserStatus.ACTIVE,
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


class TestCredentialAuthService:
    """Test cases for CredentialAuthService."""

    def should_register_user_when_valid_data_provided(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that user is registered successfully with valid data."""
        mock_user_repository.exists_by_username.return_value = False
        mock_user_repository.exists_by_email.return_value = False
        mock_user_repository.create.return_value = sample_user

        result = credential_auth_service.register_user(
            username="testuser",
            email="<EMAIL>",
            password="password123",
        )

        assert result.username == "testuser"
        assert result.email == "<EMAIL>"
        assert result.status == UserStatus.ACTIVE
        mock_user_repository.create.assert_called_once()

    def should_raise_error_when_username_already_exists(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that error is raised when username already exists."""
        mock_user_repository.exists_by_username.return_value = True

        with pytest.raises(ValueError, match="Username .* already exists"):
            credential_auth_service.register_user(
                username="testuser",
                email="<EMAIL>",
                password="password123",
            )

    def should_raise_error_when_email_already_exists(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that error is raised when email already exists."""
        mock_user_repository.exists_by_username.return_value = False
        mock_user_repository.exists_by_email.return_value = True

        with pytest.raises(ValueError, match="Email .* already exists"):
            credential_auth_service.register_user(
                username="newuser",
                email="<EMAIL>",
                password="password123",
            )

    def should_login_user_with_username_when_valid_credentials_provided(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that user can login with username and valid password."""
        # Mock password verification to return True
        from unittest.mock import patch

        with patch(
            "modules.auth.application.credential_auth_service.CredentialAuthService.verify_password",
            return_value=True,
        ):
            mock_user_repository.get_by_username_or_email.return_value = sample_user

            user, token = credential_auth_service.login(
                username_or_email="testuser",
                password="password123",
            )

            assert user.username == "testuser"
            assert token is not None
            assert isinstance(token, str)

    def should_login_user_with_email_when_valid_credentials_provided(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that user can login with email and valid password."""
        # Mock password verification to return True
        from unittest.mock import patch

        with patch(
            "modules.auth.application.credential_auth_service.CredentialAuthService.verify_password",
            return_value=True,
        ):
            mock_user_repository.get_by_username_or_email.return_value = sample_user

            user, token = credential_auth_service.login(
                username_or_email="<EMAIL>",
                password="password123",
            )

            assert user.email == "<EMAIL>"
            assert token is not None
            assert isinstance(token, str)

    def should_raise_error_when_user_not_found_for_login(
        self, credential_auth_service, mock_user_repository
    ):
        """Test that error is raised when user is not found for login."""
        mock_user_repository.get_by_username_or_email.return_value = None

        with pytest.raises(ValueError, match="Invalid username/email or password"):
            credential_auth_service.login(
                username_or_email="nonexistent",
                password="password123",
            )

    def should_raise_error_when_invalid_password_provided(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that error is raised when invalid password is provided."""
        # Mock password verification to return False
        from unittest.mock import patch

        with patch(
            "modules.auth.application.credential_auth_service.CredentialAuthService.verify_password",
            return_value=False,
        ):
            mock_user_repository.get_by_username_or_email.return_value = sample_user

            with pytest.raises(ValueError, match="Invalid username/email or password"):
                credential_auth_service.login(
                    username_or_email="testuser",
                    password="wrongpassword",
                )

    def should_get_user_by_id_when_user_exists(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that user is retrieved by ID when exists."""
        mock_user_repository.get_by_id.return_value = sample_user

        result = credential_auth_service.get_user_by_id(str(sample_user.id))

        assert result.id == sample_user.id
        assert result.username == sample_user.username

    def should_return_none_when_user_not_found_by_id(
        self, credential_auth_service, mock_user_repository
    ):
        """Test that None is returned when user is not found by ID."""
        mock_user_repository.get_by_id.return_value = None

        result = credential_auth_service.get_user_by_id(str(uuid.uuid4()))

        assert result is None

    def should_change_password_when_valid_current_password_provided(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that password is changed when valid current password is provided."""
        # Mock password verification to return True for current password
        from unittest.mock import patch

        with patch(
            "modules.auth.application.credential_auth_service.CredentialAuthService.verify_password",
            return_value=True,
        ):
            mock_user_repository.get_by_id.return_value = sample_user
            mock_user_repository.update.return_value = sample_user

            result = credential_auth_service.change_password(
                user_id=sample_user.id,
                current_password="oldpassword",
                new_password="newpassword123",
            )

            assert result is True
            mock_user_repository.update.assert_called_once()

    def should_return_false_when_invalid_current_password_provided(
        self, credential_auth_service, mock_user_repository, sample_user
    ):
        """Test that False is returned when invalid current password is provided."""
        # Mock password verification to return False for current password
        from unittest.mock import patch

        with patch(
            "modules.auth.application.credential_auth_service.CredentialAuthService.verify_password",
            return_value=False,
        ):
            mock_user_repository.get_by_id.return_value = sample_user

            result = credential_auth_service.change_password(
                user_id=sample_user.id,
                current_password="wrongpassword",
                new_password="newpassword123",
            )

            assert result is False
            mock_user_repository.update.assert_not_called()

    def should_return_false_when_user_not_found_for_password_change(
        self, credential_auth_service, mock_user_repository
    ):
        """Test that False is returned when user is not found for password change."""
        mock_user_repository.get_by_id.return_value = None

        result = credential_auth_service.change_password(
            user_id=uuid.uuid4(),
            current_password="password",
            new_password="newpassword123",
        )

        assert result is False

    def should_create_valid_jwt_token_when_user_data_provided(
        self, credential_auth_service, sample_user
    ):
        """Test that valid JWT token is created with user data."""
        token = credential_auth_service.create_access_token(sample_user)

        assert token is not None
        assert isinstance(token, str)
        assert len(token.split(".")) == 3  # JWT has 3 parts

    def should_verify_valid_jwt_token_when_correct_token_provided(
        self, credential_auth_service, sample_user
    ):
        """Test that valid JWT token is verified correctly."""
        token = credential_auth_service.create_access_token(sample_user)

        payload = credential_auth_service.verify_token(token)

        assert payload is not None
        assert payload["sub"] == str(sample_user.id)
        assert "exp" in payload

    def should_return_none_when_invalid_jwt_token_provided(
        self, credential_auth_service
    ):
        """Test that None is returned when invalid JWT token is provided."""
        payload = credential_auth_service.verify_token("invalid_token")

        assert payload is None

    def should_return_none_when_expired_jwt_token_provided(
        self, credential_auth_service, sample_user
    ):
        """Test that None is returned when expired JWT token is provided."""
        # Create service with very short expiration time
        short_service = CredentialAuthService(
            user_repository=Mock(),
            secret_key="test_secret_key",
            algorithm="HS256",
            token_expire_minutes=-1,  # Already expired
        )

        token = short_service.create_access_token(sample_user)
        payload = short_service.verify_token(token)

        assert payload is None
