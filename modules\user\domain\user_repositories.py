from abc import ABC, abstractmethod
from uuid import UUID

from .user_models import User, UserStatus


class UserRepository(ABC):
    @abstractmethod
    def get_by_id(self, user_id: UUID) -> User | None:
        """Gets a user by their ID."""
        raise NotImplementedError

    @abstractmethod
    def get_by_username(self, username: str) -> User | None:
        """Gets a user by their username."""
        raise NotImplementedError

    @abstractmethod
    def get_by_email(self, email: str) -> User | None:
        """Gets a user by their email."""
        raise NotImplementedError

    @abstractmethod
    def save(self, user: User) -> User:
        """Saves a user (creates or updates)."""
        raise NotImplementedError

    @abstractmethod
    def delete(self, user_id: UUID) -> bool:
        """Deletes a user by their ID."""
        raise NotImplementedError

    @abstractmethod
    def update_status(self, user_id: UUID, status: UserStatus) -> bool:
        """Updates a user's status."""
        raise NotImplementedError

    @abstractmethod
    def list_users(
        self, skip: int = 0, limit: int = 100, status: UserStatus | None = None
    ) -> list[User]:
        """Lists users with optional filtering."""
        raise NotImplementedError

    @abstractmethod
    def search_users(self, query: str, skip: int = 0, limit: int = 100) -> list[User]:
        """Searches users by username or email."""
        raise NotImplementedError
