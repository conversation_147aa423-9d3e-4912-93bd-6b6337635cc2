"""add status column to users table

Revision ID: f3ab7c588612
Revises: 952ef2d76c38
Create Date: 2025-06-23 18:39:19.220231

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f3ab7c588612'
down_revision = '952ef2d76c38'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Create ENUM types first
    user_status_enum = sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', name='userstatus')
    user_status_enum.create(op.get_bind())

    # First, add the status column and other fields to users table
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('status', user_status_enum, nullable=False, server_default='ACTIVE'))
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), nullable=True))
        batch_op.alter_column('hashed_password',
               existing_type=sa.VARCHAR(),
               nullable=False)
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.drop_index(batch_op.f('ix_users_id'))
        batch_op.drop_index(batch_op.f('ix_users_username'))
        batch_op.create_unique_constraint(None, ['username'])
        batch_op.create_unique_constraint(None, ['email'])
        batch_op.drop_column('organization_id')
        batch_op.drop_column('department')
        batch_op.drop_column('is_active')
        batch_op.drop_column('it_code')
        batch_op.drop_column('employee_id')
        batch_op.drop_column('role')
        batch_op.drop_column('oauth_provider')
        batch_op.drop_column('oauth_id')
        batch_op.drop_column('work_email')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('work_email', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('oauth_id', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('oauth_provider', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('role', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('employee_id', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('it_code', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('department', sa.VARCHAR(), autoincrement=False, nullable=True))
        batch_op.add_column(sa.Column('organization_id', sa.INTEGER(), autoincrement=False, nullable=True))
        batch_op.drop_constraint(None, type_='unique')
        batch_op.drop_constraint(None, type_='unique')
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=True)
        batch_op.create_index(batch_op.f('ix_users_id'), ['id'], unique=False)
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=True)
        batch_op.alter_column('hashed_password',
               existing_type=sa.VARCHAR(),
               nullable=True)
        batch_op.drop_column('updated_at')
        batch_op.drop_column('created_at')
        batch_op.drop_column('status')

    # Drop ENUM types
    user_status_enum = sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', name='userstatus')
    user_status_enum.drop(op.get_bind())

    # ### end Alembic commands ###
