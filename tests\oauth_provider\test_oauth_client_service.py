"""Tests for OAuthClientService."""

import uuid
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig
from modules.oauth_provider.domain.oauth_provider_repositories import (
    OAuthProviderRepository,
)
from shared.oauth.client_service import OAuthClientService


@pytest.fixture
def mock_oauth_provider_repo():
    """Create mock OAuth provider repository."""
    return AsyncMock(spec=OAuthProviderRepository)


@pytest.fixture
def github_provider_config():
    """Create GitHub provider configuration for testing."""
    return OAuthProviderConfig(
        id=uuid.uuid4(),
        name="github",
        display_name="GitHub",
        description="GitHub OAuth provider",
        is_enabled=True,
        icon_url="https://github.com/favicon.ico",
        client_id="test_github_client_id",
        client_secret="test_github_client_secret",
        authorize_url="https://github.com/login/oauth/authorize",
        token_url="https://github.com/login/oauth/access_token",
        user_info_url="https://api.github.com/user",
        scope="user:email",
        user_id_field="id",
        email_field="email",
        name_field="name",
        username_field="login",
        avatar_field="avatar_url",
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def google_provider_config():
    """Create Google provider configuration for testing."""
    return OAuthProviderConfig(
        id=uuid.uuid4(),
        name="google",
        display_name="Google",
        description="Google OAuth provider",
        is_enabled=True,
        icon_url="https://google.com/favicon.ico",
        client_id="test_google_client_id",
        client_secret="test_google_client_secret",
        authorize_url="https://accounts.google.com/o/oauth2/v2/auth",
        token_url="https://oauth2.googleapis.com/token",
        user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
        scope="openid email profile",
        user_id_field="id",
        email_field="email",
        name_field="name",
        username_field=None,
        avatar_field="picture",
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def oauth_client_service(mock_oauth_provider_repo):
    """Create OAuthClientService instance for testing."""
    return OAuthClientService(
        oauth_provider_repo=mock_oauth_provider_repo,
        oauth_base_url="http://localhost:8000",
    )


class TestOAuthClientService:
    """Test cases for OAuthClientService."""

    @pytest.mark.asyncio
    async def should_get_provider_config_from_database_when_not_cached(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that provider config is loaded from database when not cached."""
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config

        result = await oauth_client_service.get_provider_config("github")

        assert result == github_provider_config
        mock_oauth_provider_repo.get_by_name.assert_called_once_with("github")

    @pytest.mark.asyncio
    async def should_return_cached_provider_config_when_available(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that cached provider config is returned when available."""
        # First call loads from database
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config
        await oauth_client_service.get_provider_config("github")

        # Second call should use cache
        mock_oauth_provider_repo.reset_mock()
        result = await oauth_client_service.get_provider_config("github")

        assert result == github_provider_config
        mock_oauth_provider_repo.get_by_name.assert_not_called()

    @pytest.mark.asyncio
    async def should_return_none_when_provider_not_found(
        self, oauth_client_service, mock_oauth_provider_repo
    ):
        """Test that None is returned when provider is not found."""
        mock_oauth_provider_repo.get_by_name.return_value = None

        result = await oauth_client_service.get_provider_config("nonexistent")

        assert result is None

    @pytest.mark.asyncio
    async def should_return_none_when_provider_disabled(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that None is returned when provider is disabled."""
        github_provider_config.is_enabled = False
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config

        result = await oauth_client_service.get_provider_config("github")

        assert result is None

    @pytest.mark.asyncio
    async def should_generate_authorization_url_when_valid_provider_given(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that authorization URL is generated correctly."""
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config

        url = await oauth_client_service.generate_authorization_url("github")

        assert "https://github.com/login/oauth/authorize" in url
        assert "client_id=test_github_client_id" in url
        assert (
            "redirect_uri=http%3A%2F%2Flocalhost%3A8000%2Fapi%2Fv1%2Fauth%2Foauth%2Fgithub%2Fcallback"
            in url
        )
        assert "scope=user%3Aemail" in url
        assert "response_type=code" in url

    @pytest.mark.asyncio
    async def should_include_state_parameter_when_provided(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that state parameter is included when provided."""
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config
        state = "test_state_123"

        url = await oauth_client_service.generate_authorization_url("github", state)

        assert f"state={state}" in url

    @pytest.mark.asyncio
    async def should_raise_error_when_provider_not_found_for_authorization(
        self, oauth_client_service, mock_oauth_provider_repo
    ):
        """Test that error is raised when provider is not found."""
        mock_oauth_provider_repo.get_by_name.return_value = None

        with pytest.raises(
            ValueError, match="OAuth provider 'nonexistent' not found or disabled"
        ):
            await oauth_client_service.generate_authorization_url("nonexistent")

    @pytest.mark.asyncio
    async def should_exchange_code_for_token_when_valid_provider_and_code_given(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that authorization code is exchanged for token successfully."""
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config
        mock_token = {"access_token": "test_token", "token_type": "bearer"}

        with patch(
            "shared.oauth.client_service.AsyncOAuth2Client"
        ) as mock_oauth_client:
            mock_client_instance = MagicMock()
            mock_client_instance.fetch_token = AsyncMock(return_value=mock_token)
            mock_oauth_client.return_value = mock_client_instance

            result = await oauth_client_service.exchange_code_for_token(
                "github", "test_code"
            )

            assert result == mock_token
            mock_client_instance.fetch_token.assert_called_once()

    @pytest.mark.asyncio
    async def should_get_user_info_when_valid_provider_and_token_given(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that user info is fetched successfully."""
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config
        mock_github_response = {
            "id": 12345,
            "login": "testuser",
            "email": "<EMAIL>",
            "name": "Test User",
            "avatar_url": "https://avatar.url",
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_github_response
            mock_response.raise_for_status.return_value = None

            mock_client_instance = AsyncMock()
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance

            result = await oauth_client_service.get_user_info("github", "test_token")

            expected = {
                "oauth_id": "12345",
                "email": "<EMAIL>",
                "name": "Test User",
                "username": "testuser",
                "avatar_url": "https://avatar.url",
            }
            assert result == expected

    @pytest.mark.asyncio
    async def should_normalize_google_user_data_correctly(
        self, oauth_client_service, mock_oauth_provider_repo, google_provider_config
    ):
        """Test that Google user data is normalized correctly."""
        mock_oauth_provider_repo.get_by_name.return_value = google_provider_config
        mock_google_response = {
            "id": "67890",
            "email": "<EMAIL>",
            "name": "Test User",
            "picture": "https://picture.url",
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_response = MagicMock()
            mock_response.json.return_value = mock_google_response
            mock_response.raise_for_status.return_value = None

            mock_client_instance = AsyncMock()
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value.__aenter__.return_value = mock_client_instance

            result = await oauth_client_service.get_user_info("google", "test_token")

            expected = {
                "oauth_id": "67890",
                "email": "<EMAIL>",
                "name": "Test User",
                "username": "test",  # Generated from email prefix since Google doesn't have username field
                "avatar_url": "https://picture.url",
            }
            assert result == expected

    @pytest.mark.asyncio
    async def should_clear_cache_when_requested(
        self, oauth_client_service, mock_oauth_provider_repo, github_provider_config
    ):
        """Test that cache is cleared correctly."""
        # Load provider into cache
        mock_oauth_provider_repo.get_by_name.return_value = github_provider_config
        await oauth_client_service.get_provider_config("github")

        # Clear cache
        await oauth_client_service.clear_cache()

        # Next call should hit database again
        mock_oauth_provider_repo.reset_mock()
        await oauth_client_service.get_provider_config("github")
        mock_oauth_provider_repo.get_by_name.assert_called_once_with("github")

    @pytest.mark.asyncio
    async def should_warm_cache_with_all_enabled_providers(
        self,
        oauth_client_service,
        mock_oauth_provider_repo,
        github_provider_config,
        google_provider_config,
    ):
        """Test that cache is warmed with all enabled providers."""
        mock_oauth_provider_repo.get_all_enabled.return_value = [
            github_provider_config,
            google_provider_config,
        ]

        await oauth_client_service.warm_cache()

        stats = await oauth_client_service.get_cache_stats()
        assert stats["cache_size"] == 2
        assert "github" in stats["cached_providers"]
        assert "google" in stats["cached_providers"]
