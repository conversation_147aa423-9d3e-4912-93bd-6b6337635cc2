# AI4SE MCP Hub

基于 FastAPI 和领域驱动设计（DDD）的 AI for Software Engineering 模型与上下文管理平台。

## 项目概述

AI4SE MCP Hub 是一个现代化的 Python Web 应用，采用模块化架构设计，为 AI 软件工程工具提供统一的模型和上下文管理服务。

### 技术栈

- **Web 框架**: FastAPI 0.115.13
- **数据库**: PostgreSQL + SQLAlchemy 2.0.41
- **数据迁移**: Alembic 1.16.2
- **认证**: JWT + OAuth2
- **数据验证**: Pydantic 2.11.7
- **测试**: Pytest + 覆盖率报告
- **代码质量**: Ruff + Black + isort + MyPy
- **任务管理**: Invoke (跨平台)

### 核心特性

- **模块化架构**: 基于 DDD 的分层设计
- **用户管理**: 完整的用户注册、认证和授权
- **OAuth 集成**: 支持第三方登录
- **API 文档**: 自动生成的 OpenAPI 文档
- **数据库迁移**: 版本化的数据库结构管理
- **代码质量**: 自动化的代码检查和格式化
- **测试覆盖**: 71% 的代码覆盖率

## 快速开始

### 环境要求

- Python 3.12+
- PostgreSQL 数据库
- uv (推荐) 或 pip

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd ai4se-mcp-hub
```

2. **创建虚拟环境**
```bash
# 使用 uv (推荐)
uv venv
.\.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/macOS

# 或使用 Python
python -m venv .venv
.\.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/macOS
```

3. **安装依赖**
```bash
# 安装项目依赖
uv sync

# 安装开发工具
inv install-dev
```

4. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，配置数据库连接等
```

5. **数据库迁移**
```bash
# 运行数据库迁移
inv db-upgrade
```

6. **启动服务**
```bash
# 启动开发服务器
inv server

# 或直接使用 uvicorn
uvicorn main:app --reload
```

访问 http://localhost:8000 查看 API 文档。

## 项目结构

```
ai4se-mcp-hub/
├── modules/                 # 业务模块
│   ├── auth/               # 认证模块
│   │   ├── application/    # 应用层
│   │   ├── domain/         # 领域层
│   │   ├── infrastructure/ # 基础设施层
│   │   └── interfaces/     # 接口层
│   └── user/               # 用户模块
│       ├── application/
│       ├── domain/
│       ├── infrastructure/
│       └── interfaces/
├── common/                 # 通用代码
│   ├── db/                # 数据库配置
│   └── dependencies.py    # 依赖注入
├── tests/                  # 测试代码
├── alembic/               # 数据库迁移
├── scripts/               # 工具脚本
├── tasks.py               # 任务管理
├── main.py                # 应用入口
└── pyproject.toml         # 项目配置
```

## 开发工具

项目使用 **Invoke** 作为跨平台任务管理工具，替代传统的 Makefile。

### 查看所有可用任务
```bash
# 列出所有任务
inv --list

# 显示详细帮助
inv help
```

### 代码质量管理
```bash
# 代码格式化
inv format

# 代码检查
inv lint

# 完整优化
inv optimize

# 仅检查（不修改）
inv check
```

### 测试相关
```bash
# 运行测试
inv test

# 测试覆盖率报告
inv test-cov
```

### 数据库操作
```bash
# 运行迁移
inv db-upgrade

# 回滚迁移
inv db-downgrade
```

### 开发服务
```bash
# 启动开发服务器
inv server

# 清理缓存文件
inv clean

# 项目信息
inv info
```

### 工作流
```bash
# 提交前检查
inv pre-commit

# CI 检查
inv ci
```

## 配置说明

### 环境变量

在 `.env` 文件中配置以下变量：

```bash
# 应用配置
AI4SE_MCP_HUB_DEBUG=True
AI4SE_MCP_HUB_HOST=127.0.0.1
AI4SE_MCP_HUB_PORT=8000

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/ai4se_mcp_hub

# JWT 配置
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 代码质量工具

所有工具配置都在 `pyproject.toml` 中：

- **Ruff**: 现代 Python 代码检查和格式化
- **Black**: 代码格式化
- **isort**: 导入排序
- **MyPy**: 类型检查
- **autoflake**: 清理未使用的导入

## 开发工作流

### 提交前检查
```bash
# 1. 代码优化
inv optimize

# 2. 运行测试
inv test

# 3. 最终检查
inv check
```

### 或使用组合命令
```bash
# 运行所有提交前检查
inv pre-commit
```

## 测试

项目使用 Pytest 进行测试，当前测试覆盖率为 71%。

```bash
# 运行所有测试
inv test

# 生成覆盖率报告
inv test-cov

# 查看 HTML 覆盖率报告
open htmlcov/index.html
```

## API 文档

启动服务后，可以访问：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 运行代码质量检查 (`inv pre-commit`)
4. 提交更改 (`git commit -m 'feat: add amazing feature'`)
5. 推送到分支 (`git push origin feature/amazing-feature`)
6. 创建 Pull Request

### 提交信息规范

使用约定式提交格式：

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式化
- `refactor`: 代码重构
- `test`: 测试相关
- `build`: 构建系统

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 相关链接

- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [SQLAlchemy 文档](https://docs.sqlalchemy.org/)
- [Pydantic 文档](https://docs.pydantic.dev/)
- [Invoke 文档](https://docs.pyinvoke.org/)

---

**开发愉快！**

如有问题，请查看项目文档或提交 Issue。
