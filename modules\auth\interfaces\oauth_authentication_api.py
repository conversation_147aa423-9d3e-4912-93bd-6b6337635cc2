"""OAuth authentication API endpoints."""

import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status

from modules.auth.application.oauth_authentication_service import (
    OAuthAuthenticationService,
)
from modules.auth.interfaces.credential_auth_schemas import LoginResponse, UserResponse
from modules.auth.interfaces.dependencies import (
    get_current_user,
    get_oauth_authentication_service,
)
from modules.auth.interfaces.oauth_authentication_schemas import (
    OAuthAccountResponse,
    OAuthAuthorizationResponse,
    OAuthProviderInfo,
    OAuthProvidersResponse,
    UserOAuthAccountsResponse,
)
from modules.user.domain.user_models import User

router = APIRouter(prefix="/auth/oauth", tags=["OAuth Authentication"])


@router.get(
    "/providers",
    response_model=OAuthProvidersResponse,
    summary="Get available OAuth providers",
    description="Get list of all available OAuth providers for authentication",
)
async def get_oauth_providers(
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> OAuthProvidersResponse:
    """Get list of available OAuth providers."""
    providers_data = await oauth_service.get_available_providers()

    providers = [
        OAuthProviderInfo(
            name=provider["name"],
            display_name=provider["display_name"],
            description=provider["description"],
            icon_url=provider["icon_url"],
        )
        for provider in providers_data
    ]

    return OAuthProvidersResponse(providers=providers)


@router.get(
    "/{provider}/authorize",
    response_model=OAuthAuthorizationResponse,
    summary="Get OAuth authorization URL",
    description="Generate OAuth authorization URL for the specified provider",
)
async def get_oauth_authorization_url(
    provider: str,
    state: str | None = Query(None, description="State parameter for CSRF protection"),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> OAuthAuthorizationResponse:
    """Generate OAuth authorization URL for the specified provider."""
    try:
        authorization_url = await oauth_service.get_authorization_url(provider, state)
        return OAuthAuthorizationResponse(
            authorization_url=authorization_url,
            state=state,
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        ) from e


@router.get(
    "/{provider}/callback",
    response_model=LoginResponse,
    summary="Handle OAuth callback",
    description="Handle OAuth callback and authenticate user",
)
async def handle_oauth_callback(
    provider: str,
    code: str = Query(..., description="Authorization code from OAuth provider"),
    state: str | None = Query(None, description="State parameter for CSRF protection"),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> LoginResponse:
    """Handle OAuth callback and authenticate user."""
    try:
        user, access_token = await oauth_service.handle_oauth_callback(
            provider=provider,
            code=code,
        )

        user_response = UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            status=user.status.value,
            created_at=user.created_at.isoformat(),
            updated_at=user.updated_at.isoformat(),
        )

        return LoginResponse(
            access_token=access_token,
            token_type="bearer",
            user=user_response,
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e


@router.get(
    "/accounts",
    response_model=UserOAuthAccountsResponse,
    summary="Get user's OAuth accounts",
    description="Get all OAuth accounts linked to the current user",
)
async def get_user_oauth_accounts(
    current_user: User = Depends(get_current_user),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> UserOAuthAccountsResponse:
    """Get all OAuth accounts linked to the current user."""
    oauth_accounts = oauth_service.get_user_oauth_accounts(current_user.id)

    accounts_response = [
        OAuthAccountResponse(
            id=account.id,
            provider=account.provider,
            provider_account_id=account.provider_account_id,
            created_at=account.created_at.isoformat(),
        )
        for account in oauth_accounts
    ]

    return UserOAuthAccountsResponse(oauth_accounts=accounts_response)


@router.delete(
    "/accounts/{oauth_account_id}",
    status_code=status.HTTP_200_OK,
    summary="Unlink OAuth account",
    description="Unlink an OAuth account from the current user",
)
async def unlink_oauth_account(
    oauth_account_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    oauth_service: OAuthAuthenticationService = Depends(
        get_oauth_authentication_service
    ),
) -> dict:
    """Unlink an OAuth account from the current user."""
    try:
        success = oauth_service.unlink_oauth_account(
            user_id=current_user.id,
            oauth_account_id=oauth_account_id,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="OAuth account not found or does not belong to current user",
            )

        return {"message": "OAuth account unlinked successfully"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        ) from e
