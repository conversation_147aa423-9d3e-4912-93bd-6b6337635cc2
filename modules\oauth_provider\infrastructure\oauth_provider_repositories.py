"""OAuth Provider repository implementations."""

from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session

from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig
from modules.oauth_provider.domain.oauth_provider_repositories import (
    OAuthProviderRepository,
)
from modules.oauth_provider.infrastructure.oauth_provider_orm import OAuthProviderORM


class OAuthProviderRepositoryImpl(OAuthProviderRepository):
    """SQLAlchemy implementation of OAuth provider repository."""

    def __init__(self, session: Session):
        self.session = session

    def create(self, provider_config: OAuthProviderConfig) -> OAuthProviderConfig:
        """Create a new OAuth provider configuration."""
        orm_obj = OAuthProviderORM(
            id=provider_config.id,
            name=provider_config.name,
            display_name=provider_config.display_name,
            description=provider_config.description,
            is_enabled=provider_config.is_enabled,
            icon_url=str(provider_config.icon_url)
            if provider_config.icon_url
            else None,
            client_id=provider_config.client_id,
            client_secret=provider_config.client_secret,
            authorize_url=str(provider_config.authorize_url),
            token_url=str(provider_config.token_url),
            user_info_url=str(provider_config.user_info_url),
            scope=provider_config.scope,
            user_id_field=provider_config.user_id_field,
            email_field=provider_config.email_field,
            name_field=provider_config.name_field,
            username_field=provider_config.username_field,
            avatar_field=provider_config.avatar_field,
            created_at=provider_config.created_at,
            updated_at=provider_config.updated_at,
        )

        self.session.add(orm_obj)
        self.session.flush()
        self.session.refresh(orm_obj)

        return self._orm_to_domain(orm_obj)

    def get_by_name(self, name: str) -> OAuthProviderConfig | None:
        """Get OAuth provider configuration by name."""
        stmt = select(OAuthProviderORM).where(OAuthProviderORM.name == name)
        result = self.session.execute(stmt)
        orm_obj = result.scalar_one_or_none()

        return self._orm_to_domain(orm_obj) if orm_obj else None

    def get_by_id(self, provider_id: UUID) -> OAuthProviderConfig | None:
        """Get OAuth provider configuration by ID."""
        stmt = select(OAuthProviderORM).where(OAuthProviderORM.id == provider_id)
        result = self.session.execute(stmt)
        orm_obj = result.scalar_one_or_none()

        return self._orm_to_domain(orm_obj) if orm_obj else None

    def get_all_enabled(self) -> list[OAuthProviderConfig]:
        """Get all enabled OAuth provider configurations."""
        stmt = select(OAuthProviderORM).where(OAuthProviderORM.is_enabled)
        result = self.session.execute(stmt)
        orm_objs = result.scalars().all()

        return [self._orm_to_domain(orm_obj) for orm_obj in orm_objs]

    def get_all(self) -> list[OAuthProviderConfig]:
        """Get all OAuth provider configurations."""
        stmt = select(OAuthProviderORM)
        result = self.session.execute(stmt)
        orm_objs = result.scalars().all()

        return [self._orm_to_domain(orm_obj) for orm_obj in orm_objs]

    def update(self, provider_config: OAuthProviderConfig) -> OAuthProviderConfig:
        """Update an OAuth provider configuration."""
        stmt = select(OAuthProviderORM).where(OAuthProviderORM.id == provider_config.id)
        result = self.session.execute(stmt)
        orm_obj = result.scalar_one_or_none()

        if not orm_obj:
            raise ValueError(f"OAuth provider with ID {provider_config.id} not found")

        # Update fields
        orm_obj.name = provider_config.name
        orm_obj.display_name = provider_config.display_name
        orm_obj.description = provider_config.description
        orm_obj.is_enabled = provider_config.is_enabled
        orm_obj.icon_url = (
            str(provider_config.icon_url) if provider_config.icon_url else None
        )
        orm_obj.client_id = provider_config.client_id
        orm_obj.client_secret = provider_config.client_secret
        orm_obj.authorize_url = str(provider_config.authorize_url)
        orm_obj.token_url = str(provider_config.token_url)
        orm_obj.user_info_url = str(provider_config.user_info_url)
        orm_obj.scope = provider_config.scope
        orm_obj.user_id_field = provider_config.user_id_field
        orm_obj.email_field = provider_config.email_field
        orm_obj.name_field = provider_config.name_field
        orm_obj.username_field = provider_config.username_field
        orm_obj.avatar_field = provider_config.avatar_field
        orm_obj.updated_at = provider_config.updated_at

        self.session.flush()
        self.session.refresh(orm_obj)

        return self._orm_to_domain(orm_obj)

    def delete(self, provider_id: UUID) -> bool:
        """Delete an OAuth provider configuration."""
        stmt = select(OAuthProviderORM).where(OAuthProviderORM.id == provider_id)
        result = self.session.execute(stmt)
        orm_obj = result.scalar_one_or_none()

        if not orm_obj:
            return False

        self.session.delete(orm_obj)
        self.session.flush()

        return True

    def exists_by_name(self, name: str) -> bool:
        """Check if a provider with the given name exists."""
        stmt = select(OAuthProviderORM.id).where(OAuthProviderORM.name == name)
        result = self.session.execute(stmt)
        return result.scalar_one_or_none() is not None

    def _orm_to_domain(self, orm_obj: OAuthProviderORM) -> OAuthProviderConfig:
        """Convert ORM object to domain model."""
        return OAuthProviderConfig(
            id=orm_obj.id,
            name=orm_obj.name,
            display_name=orm_obj.display_name,
            description=orm_obj.description,
            is_enabled=orm_obj.is_enabled,
            icon_url=orm_obj.icon_url,
            client_id=orm_obj.client_id,
            client_secret=orm_obj.client_secret,
            authorize_url=orm_obj.authorize_url,
            token_url=orm_obj.token_url,
            user_info_url=orm_obj.user_info_url,
            scope=orm_obj.scope,
            user_id_field=orm_obj.user_id_field,
            email_field=orm_obj.email_field,
            name_field=orm_obj.name_field,
            username_field=orm_obj.username_field,
            avatar_field=orm_obj.avatar_field,
            created_at=orm_obj.created_at,
            updated_at=orm_obj.updated_at,
        )
