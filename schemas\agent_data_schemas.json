{"workflow_data_schemas": {"description": "AI开发工作流各Agent间的标准JSON数据结构定义", "rules_processor_output": {"description": "规则处理Agent的输出格式", "type": "object", "properties": {"content_type": {"type": "string", "enum": ["processed_rules"], "description": "内容类型标识"}, "processed_rules": {"type": "string", "description": "处理后的规则内容（Markdown格式）"}, "rule_categories": {"type": "object", "properties": {"core_principles": {"type": "array", "items": {"type": "string"}}, "technical_specifications": {"type": "array", "items": {"type": "string"}}, "architectural_constraints": {"type": "array", "items": {"type": "string"}}, "coding_standards": {"type": "array", "items": {"type": "string"}}, "quality_requirements": {"type": "array", "items": {"type": "string"}}, "engineering_practices": {"type": "array", "items": {"type": "string"}}}}, "metadata": {"type": "object", "properties": {"total_rules": {"type": "integer"}, "processing_timestamp": {"type": "string"}, "source_files": {"type": "array", "items": {"type": "string"}}}}}, "required": ["content_type", "processed_rules", "rule_categories"]}, "business_analyzer_output": {"description": "业务分析Agent的输出格式", "type": "object", "properties": {"content_type": {"type": "string", "enum": ["business_analysis"], "description": "内容类型标识"}, "business_overview": {"type": "object", "properties": {"project_name": {"type": "string"}, "project_description": {"type": "string"}, "core_objectives": {"type": "array", "items": {"type": "string"}}, "target_users": {"type": "array", "items": {"type": "string"}}, "value_proposition": {"type": "array", "items": {"type": "string"}}}, "required": ["project_name", "project_description", "core_objectives"]}, "core_entities": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "key_attributes": {"type": "array", "items": {"type": "string"}}, "business_rules": {"type": "array", "items": {"type": "string"}}, "relationships": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "description"]}}, "functional_requirements": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "priority": {"type": "string", "enum": ["high", "medium", "low"]}, "acceptance_criteria": {"type": "array", "items": {"type": "string"}}}, "required": ["id", "title", "description", "priority"]}}, "business_rules": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "category": {"type": "string"}, "rule": {"type": "string"}, "trigger_condition": {"type": "string"}, "execution_action": {"type": "string"}}, "required": ["id", "category", "rule"]}}, "user_stories": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}, "acceptance_criteria": {"type": "array", "items": {"type": "string"}}, "priority": {"type": "string", "enum": ["high", "medium", "low"]}}, "required": ["id", "title", "description"]}}}, "required": ["content_type", "business_overview", "core_entities", "functional_requirements"]}, "domain_modeler_output": {"description": "领域建模Agent的输出格式", "type": "object", "properties": {"content_type": {"type": "string", "enum": ["domain_model"], "description": "内容类型标识"}, "concept_analysis": {"type": "object", "properties": {"similar_concepts": {"type": "array", "items": {"type": "object", "properties": {"concept_group": {"type": "string"}, "similar_terms": {"type": "array", "items": {"type": "string"}}, "recommended_approach": {"type": "string"}, "final_concept_name": {"type": "string"}, "rationale": {"type": "string"}}, "required": ["concept_group", "similar_terms", "final_concept_name"]}}, "modeling_decisions": {"type": "array", "items": {"type": "object", "properties": {"decision": {"type": "string"}, "rationale": {"type": "string"}, "impact": {"type": "string"}}, "required": ["decision", "rationale"]}}}}, "bounded_contexts": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "responsibilities": {"type": "array", "items": {"type": "string"}}, "relationships": {"type": "array", "items": {"type": "object", "properties": {"target_context": {"type": "string"}, "relationship_type": {"type": "string"}, "description": {"type": "string"}}}}}, "required": ["name", "description", "responsibilities"]}}, "aggregates": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "context": {"type": "string"}, "aggregate_root": {"type": "string"}, "entities": {"type": "array", "items": {"type": "string"}}, "value_objects": {"type": "array", "items": {"type": "string"}}, "business_rules": {"type": "array", "items": {"type": "string"}}, "invariants": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "context", "aggregate_root"]}}, "domain_entities": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "aggregate": {"type": "string"}, "description": {"type": "string"}, "attributes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "required": {"type": "boolean"}, "description": {"type": "string"}}, "required": ["name", "type", "description"]}}, "business_methods": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}, "return_type": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}}, "business_rules": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "aggregate", "description", "attributes"]}}, "value_objects": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "attributes": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "type", "description"]}}, "validation_rules": {"type": "array", "items": {"type": "string"}}, "immutable": {"type": "boolean", "default": true}}, "required": ["name", "description", "attributes"]}}, "domain_services": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "context": {"type": "string"}, "description": {"type": "string"}, "methods": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}, "return_type": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}}, "dependencies": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "context", "description"]}}, "repositories": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "managed_aggregate": {"type": "string"}, "description": {"type": "string"}, "methods": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "parameters": {"type": "array", "items": {"type": "string"}}, "return_type": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"]}}}, "required": ["name", "managed_aggregate", "description"]}}, "domain_events": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "trigger_conditions": {"type": "array", "items": {"type": "string"}}, "event_data": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "type", "description"]}}, "handlers": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "description", "trigger_conditions"]}}}, "required": ["content_type", "bounded_contexts", "aggregates", "domain_entities"]}, "requirements_generator_output": {"description": "需求生成Agent的输出格式", "type": "object", "properties": {"content_type": {"type": "string", "enum": ["technical_requirements"], "description": "内容类型标识"}, "modules": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "bounded_context": {"type": "string"}, "description": {"type": "string"}, "aggregates": {"type": "array", "items": {"type": "string"}}, "technical_requirements": {"type": "object", "properties": {"domain_layer": {"type": "object", "properties": {"entities": {"type": "array", "items": {"type": "string"}}, "value_objects": {"type": "array", "items": {"type": "string"}}, "repositories": {"type": "array", "items": {"type": "string"}}, "domain_services": {"type": "array", "items": {"type": "string"}}, "domain_events": {"type": "array", "items": {"type": "string"}}}}, "application_layer": {"type": "object", "properties": {"services": {"type": "array", "items": {"type": "string"}}, "dtos": {"type": "array", "items": {"type": "string"}}, "use_cases": {"type": "array", "items": {"type": "string"}}}}, "infrastructure_layer": {"type": "object", "properties": {"repository_implementations": {"type": "array", "items": {"type": "string"}}, "orm_models": {"type": "array", "items": {"type": "string"}}, "external_services": {"type": "array", "items": {"type": "string"}}}}, "interface_layer": {"type": "object", "properties": {"api_endpoints": {"type": "array", "items": {"type": "string"}}, "request_schemas": {"type": "array", "items": {"type": "string"}}, "response_schemas": {"type": "array", "items": {"type": "string"}}}}}}, "implementation_priority": {"type": "string", "enum": ["high", "medium", "low"]}, "dependencies": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "bounded_context", "description", "technical_requirements"]}}, "cross_cutting_concerns": {"type": "object", "properties": {"authentication": {"type": "array", "items": {"type": "string"}}, "authorization": {"type": "array", "items": {"type": "string"}}, "logging": {"type": "array", "items": {"type": "string"}}, "error_handling": {"type": "array", "items": {"type": "string"}}, "validation": {"type": "array", "items": {"type": "string"}}, "caching": {"type": "array", "items": {"type": "string"}}}}, "database_design": {"type": "object", "properties": {"tables": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "entity": {"type": "string"}, "columns": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "nullable": {"type": "boolean"}, "primary_key": {"type": "boolean"}, "foreign_key": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "type", "description"]}}, "indexes": {"type": "array", "items": {"type": "string"}}, "constraints": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "entity", "columns"]}}, "relationships": {"type": "array", "items": {"type": "object", "properties": {"from_table": {"type": "string"}, "to_table": {"type": "string"}, "relationship_type": {"type": "string"}, "description": {"type": "string"}}, "required": ["from_table", "to_table", "relationship_type"]}}}}, "api_design": {"type": "object", "properties": {"endpoints": {"type": "array", "items": {"type": "object", "properties": {"path": {"type": "string"}, "method": {"type": "string"}, "description": {"type": "string"}, "module": {"type": "string"}, "request_schema": {"type": "string"}, "response_schema": {"type": "string"}, "authentication_required": {"type": "boolean"}, "authorization_roles": {"type": "array", "items": {"type": "string"}}}, "required": ["path", "method", "description", "module"]}}, "common_schemas": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}, "fields": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "type": {"type": "string"}, "required": {"type": "boolean"}, "description": {"type": "string"}}, "required": ["name", "type", "description"]}}}, "required": ["name", "description", "fields"]}}}}}, "required": ["content_type", "modules"]}, "prompt_builder_output": {"description": "提示构建Agent的输出格式", "type": "object", "properties": {"content_type": {"type": "string", "enum": ["ai_prompts"], "description": "内容类型标识"}, "generated_prompts": {"type": "array", "items": {"type": "object", "properties": {"module_name": {"type": "string"}, "prompt_file": {"type": "string"}, "requirements_file": {"type": "string"}, "description": {"type": "string"}, "implementation_order": {"type": "integer"}}, "required": ["module_name", "prompt_file", "requirements_file"]}}, "workflow_summary": {"type": "object", "properties": {"total_modules": {"type": "integer"}, "total_prompts": {"type": "integer"}, "implementation_sequence": {"type": "array", "items": {"type": "string"}}, "estimated_effort": {"type": "string"}, "key_dependencies": {"type": "array", "items": {"type": "string"}}}}, "output_files": {"type": "object", "properties": {"prompt_files": {"type": "array", "items": {"type": "string"}}, "requirement_files": {"type": "array", "items": {"type": "string"}}, "summary_file": {"type": "string"}, "output_directory": {"type": "string"}}}}, "required": ["content_type", "generated_prompts", "output_files"]}}}