#!/usr/bin/env python3
"""Clear test data from database for OAuth testing."""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Load environment variables
load_dotenv()


def clear_test_data():
    """Clear users and oauth_accounts data from database."""

    # Get database URL
    db_url = os.getenv("AI4SE_MCP_HUB_DB_URL")
    if not db_url:
        print("❌ Error: AI4SE_MCP_HUB_DB_URL environment variable not set")
        return False

    try:
        # Create engine and session
        engine = create_engine(db_url)
        session_local = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        with session_local() as session:
            print("🧹 Clearing test data...")

            # Clear oauth_accounts first (due to foreign key constraint)
            result = session.execute(text("DELETE FROM oauth_accounts"))
            oauth_deleted = result.rowcount
            print(f"   Deleted {oauth_deleted} OAuth accounts")

            # Clear users
            result = session.execute(text("DELETE FROM users"))
            users_deleted = result.rowcount
            print(f"   Deleted {users_deleted} users")

            # Commit changes
            session.commit()

            print("✅ Test data cleared successfully!")
            return True

    except Exception as e:
        print(f"❌ Error clearing test data: {e}")
        return False


if __name__ == "__main__":
    clear_test_data()
