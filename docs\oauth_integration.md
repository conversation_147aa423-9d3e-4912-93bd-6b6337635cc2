# OAuth Integration Guide

本文档描述了如何在 AI4SE MCP Hub 中使用 GitHub 和 Google OAuth 登录功能。

## 概述

系统支持以下 OAuth 提供商：
- **GitHub**: 用于开发者账户登录
- **Google**: 用于通用 Google 账户登录

## 配置

### 环境变量

在 `.env` 文件中配置以下环境变量：

```bash
# GitHub OAuth settings
AI4SE_MCP_HUB_GITHUB_CLIENT_ID=your-github-client-id
AI4SE_MCP_HUB_GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth settings
AI4SE_MCP_HUB_GOOGLE_CLIENT_ID=your-google-client-id
AI4SE_MCP_HUB_GOOGLE_CLIENT_SECRET=your-google-client-secret

# OAuth redirect URI
AI4SE_MCP_HUB_OAUTH_REDIRECT_URI=http://localhost:8000/api/v1/auth/oauth/callback
```

### OAuth 应用设置

#### GitHub OAuth App

1. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
2. 创建新的 OAuth App
3. 设置以下信息：
   - **Application name**: AI4SE MCP Hub
   - **Homepage URL**: `http://localhost:8000`
   - **Authorization callback URL**: `http://localhost:8000/api/v1/auth/oauth/callback`
4. 获取 Client ID 和 Client Secret

#### Google OAuth App

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google+ API
4. 创建 OAuth 2.0 客户端 ID
5. 设置以下信息：
   - **应用类型**: Web 应用
   - **授权重定向 URI**: `http://localhost:8000/api/v1/auth/oauth/callback`
6. 获取客户端 ID 和客户端密钥

## API 端点

### 1. 获取授权 URL

**GET** `/api/v1/auth/oauth/authorize/{provider}`

生成 OAuth 授权 URL，用户需要访问此 URL 进行授权。

#### 参数

- `provider` (path): OAuth 提供商名称 (`github` 或 `google`)
- `state` (query, 可选): CSRF 保护的状态参数

#### 响应

```json
{
  "authorization_url": "https://github.com/login/oauth/authorize?client_id=...",
  "provider": "github",
  "state": "random-state-string"
}
```

### 2. 处理授权回调

**POST** `/api/v1/auth/oauth/{provider}/callback`

处理 OAuth 提供商的授权回调，交换授权码获取访问令牌。

#### 参数

- `provider` (path): OAuth 提供商名称 (`github` 或 `google`)

#### 请求体

```json
{
  "code": "authorization_code_from_provider",
  "state": "optional_state_parameter"
}
```

#### 响应

```json
{
  "access_token": "jwt_access_token",
  "token_type": "bearer"
}
```

## 使用流程

### 前端集成示例

```javascript
// 1. 获取授权 URL
const response = await fetch('/api/v1/auth/oauth/authorize/github?state=random123');
const { authorization_url } = await response.json();

// 2. 重定向用户到授权 URL
window.location.href = authorization_url;

// 3. 用户授权后，OAuth 提供商会重定向到回调 URL
// 前端需要捕获授权码并发送到后端

// 4. 交换授权码获取访问令牌
const callbackResponse = await fetch('/api/v1/auth/oauth/github/callback', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    code: 'authorization_code_from_url',
    state: 'random123'
  })
});

const { access_token } = await callbackResponse.json();

// 5. 使用访问令牌进行后续 API 调用
const protectedResponse = await fetch('/api/v1/auth/protected', {
  headers: {
    'Authorization': `Bearer ${access_token}`
  }
});
```

## 安全考虑

1. **状态参数**: 建议在授权请求中包含随机状态参数以防止 CSRF 攻击
2. **HTTPS**: 生产环境中必须使用 HTTPS
3. **客户端密钥**: 确保 OAuth 客户端密钥安全存储，不要暴露在前端代码中
4. **令牌过期**: JWT 访问令牌有过期时间，需要适当处理令牌刷新

## 错误处理

### 常见错误

- **400 Bad Request**: 无效的授权码或提供商
- **401 Unauthorized**: 授权失败或令牌无效
- **422 Unprocessable Entity**: 不支持的 OAuth 提供商
- **500 Internal Server Error**: 服务器内部错误

### 错误响应格式

```json
{
  "detail": "Error description"
}
```

## 数据库模式

OAuth 账户信息存储在 `oauth_accounts` 表中：

```sql
CREATE TABLE oauth_accounts (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    provider VARCHAR(50) NOT NULL,
    provider_account_id VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(provider, provider_account_id)
);
```

## 测试

运行 OAuth 相关测试：

```bash
# 运行所有认证测试
pytest tests/auth/

# 运行 OAuth 客户端服务测试
pytest tests/auth/test_oauth_client_service.py

# 运行 OAuth API 测试
pytest tests/auth/test_api.py -k oauth
```
