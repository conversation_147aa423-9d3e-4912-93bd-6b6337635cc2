"""Tests for credential authentication domain models."""

import uuid
from datetime import datetime

import pytest

from modules.user.domain.user_models import User, UserStatus


class TestUser:
    """Test cases for User domain model."""

    def should_create_user_with_valid_data(self):
        """Test that User can be created with valid data."""
        user_id = uuid.uuid4()
        now = datetime.now()

        user = User(
            id=user_id,
            username="testuser",
            email="<EMAIL>",
            hashed_password="$2b$12$hashed_password",
            status=UserStatus.ACTIVE,
            created_at=now,
            updated_at=now,
        )

        assert user.id == user_id
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert user.hashed_password == "$2b$12$hashed_password"
        assert user.status == UserStatus.ACTIVE
        assert user.created_at == now
        assert user.updated_at == now

    def should_create_user_with_default_status_when_not_specified(self):
        """Test that User is created with ACTIVE status by default."""
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed",
        )

        assert user.status == UserStatus.ACTIVE

    def should_create_user_with_default_timestamps_when_not_specified(self):
        """Test that User is created with current timestamps by default."""
        before_creation = datetime.now()

        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed",
        )

        after_creation = datetime.now()

        assert before_creation <= user.created_at <= after_creation
        assert before_creation <= user.updated_at <= after_creation

    def should_allow_user_status_change(self):
        """Test that user status can be changed."""
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed",
            status=UserStatus.ACTIVE,
        )

        user.status = UserStatus.INACTIVE

        assert user.status == UserStatus.INACTIVE

    def should_update_timestamp_when_user_is_modified(self):
        """Test that updated_at timestamp is updated when user is modified."""
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed",
        )

        original_updated_at = user.updated_at

        # Simulate time passing
        import time

        time.sleep(0.001)

        user.updated_at = datetime.now()

        assert user.updated_at > original_updated_at

    def should_validate_email_format(self):
        """Test that email format is validated."""
        # This test assumes email validation is implemented in the model
        # If using Pydantic, this would be automatic
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed",
        )

        assert "@" in user.email
        assert "." in user.email

    def should_require_non_empty_username(self):
        """Test that username cannot be empty."""
        with pytest.raises((ValueError, TypeError)):
            User(
                id=uuid.uuid4(),
                username="",
                email="<EMAIL>",
                hashed_password="hashed",
            )

    def should_require_non_empty_email(self):
        """Test that empty email is converted to None."""
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="",
            hashed_password="hashed",
        )
        # Empty email should be converted to None
        assert user.email is None

    def should_require_non_empty_password_hash(self):
        """Test that password hash cannot be empty."""
        with pytest.raises((ValueError, TypeError)):
            User(
                id=uuid.uuid4(),
                username="testuser",
                email="<EMAIL>",
                hashed_password="",
            )

    def should_have_string_representation(self):
        """Test that User has a meaningful string representation."""
        user = User(
            id=uuid.uuid4(),
            username="testuser",
            email="<EMAIL>",
            hashed_password="hashed",
        )

        str_repr = str(user)

        assert "testuser" in str_repr or "<EMAIL>" in str_repr

    def should_support_equality_comparison(self):
        """Test that User supports equality comparison based on ID."""
        user_id = uuid.uuid4()

        user1 = User(
            id=user_id,
            username="testuser1",
            email="<EMAIL>",
            hashed_password="hashed1",
        )

        user2 = User(
            id=user_id,
            username="testuser2",
            email="<EMAIL>",
            hashed_password="hashed2",
        )

        user3 = User(
            id=uuid.uuid4(),
            username="testuser1",
            email="<EMAIL>",
            hashed_password="hashed1",
        )

        assert user1 == user2  # Same ID
        assert user1 != user3  # Different ID


class TestUserStatus:
    """Test cases for UserStatus enum."""

    def should_have_active_status(self):
        """Test that ACTIVE status exists."""
        assert UserStatus.ACTIVE is not None
        assert UserStatus.ACTIVE.value == "active"

    def should_have_inactive_status(self):
        """Test that INACTIVE status exists."""
        assert UserStatus.INACTIVE is not None
        assert UserStatus.INACTIVE.value == "inactive"

    def should_have_suspended_status(self):
        """Test that SUSPENDED status exists."""
        assert UserStatus.SUSPENDED is not None
        assert UserStatus.SUSPENDED.value == "suspended"

    def should_support_string_conversion(self):
        """Test that UserStatus can be converted to string."""
        assert str(UserStatus.ACTIVE) == "UserStatus.ACTIVE"
        assert str(UserStatus.INACTIVE) == "UserStatus.INACTIVE"
        assert str(UserStatus.SUSPENDED) == "UserStatus.SUSPENDED"

    def should_support_value_access(self):
        """Test that UserStatus values can be accessed."""
        assert UserStatus.ACTIVE.value == "active"
        assert UserStatus.INACTIVE.value == "inactive"
        assert UserStatus.SUSPENDED.value == "suspended"

    def should_support_comparison(self):
        """Test that UserStatus supports comparison."""
        assert UserStatus.ACTIVE == UserStatus.ACTIVE
        assert UserStatus.ACTIVE != UserStatus.INACTIVE
        assert UserStatus.INACTIVE != UserStatus.SUSPENDED

    def should_be_iterable(self):
        """Test that UserStatus enum is iterable."""
        statuses = list(UserStatus)

        assert len(statuses) == 3
        assert UserStatus.ACTIVE in statuses
        assert UserStatus.INACTIVE in statuses
        assert UserStatus.SUSPENDED in statuses
