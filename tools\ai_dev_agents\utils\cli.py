"""
Command Line Interface for AI Development Agents

Provides CLI access to the AI development workflow.
"""

import argparse
import sys
import json
import platform
import subprocess
import shutil
from pathlib import Path
from typing import List, Optional
from datetime import datetime

# Import orchestrator - try both versions for compatibility
try:
    from ..core.orchestrator import AIDevWorkflowOrchestrator
    ORCHESTRATOR_CLASS = AIDevWorkflowOrchestrator
except ImportError:
    try:
        # Fallback for direct execution
        import sys
        from pathlib import Path
        sys.path.append(str(Path(__file__).parent.parent.parent))
        from tools.ai_dev_agents.core.orchestrator import Orchestrator
        from tools.ai_dev_agents.agents.business_analyzer import BusinessAnalyzerAgent
        from tools.ai_dev_agents.agents.domain_modeler import DomainModelerAgent
        from tools.ai_dev_agents.agents.requirements_analyzer import RequirementsAnalyzerAgent
        from tools.ai_dev_agents.agents.technical_leader import TechnicalLeaderAgent
        from tools.ai_dev_agents.agents.result_generator import ResultGeneratorAgent
        from tools.ai_dev_agents.agents.presentation_generator import PresentationGeneratorAgent
        from tools.ai_dev_agents.utils.config_manager import ConfigManager
        ORCHESTRATOR_CLASS = Orchestrator
    except ImportError:
        # If both fail, set to None and handle in functions
        ORCHESTRATOR_CLASS = None


def list_input_files(input_dir: str = "input") -> List[str]:
    """List available input files in the input directory."""
    input_path = Path(input_dir)
    if not input_path.exists():
        return []

    files = []
    for file_path in input_path.iterdir():
        if file_path.is_file() and not file_path.name.startswith('.'):
            files.append(str(file_path))

    return sorted(files)


def interactive_file_selection(input_dir: str = "input") -> Optional[str]:
    """Interactive file selection from input directory."""
    files = list_input_files(input_dir)

    if not files:
        print(f"[ERROR] No input files found in '{input_dir}' directory.")
        return None

    print(f"\n[INFO] Available input files in '{input_dir}':")
    for i, file_path in enumerate(files, 1):
        file_name = Path(file_path).name
        file_size = Path(file_path).stat().st_size
        print(f"  {i}. {file_name} ({file_size} bytes)")

    while True:
        try:
            choice = input(f"\n[INPUT] Please select a file (1-{len(files)}) or 'q' to quit: ").strip()

            if choice.lower() == 'q':
                print("Goodbye!")
                sys.exit(0)

            index = int(choice) - 1
            if 0 <= index < len(files):
                selected_file = files[index]
                print(f"[SUCCESS] Selected: {Path(selected_file).name}")
                return selected_file
            else:
                print(f"[ERROR] Invalid choice. Please enter a number between 1 and {len(files)}.")

        except ValueError:
            print("[ERROR] Invalid input. Please enter a number or 'q' to quit.")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            sys.exit(0)


def list_rules_files(rules_dir: str = "rules") -> List[str]:
    """List available rules files in the rules directory."""
    rules_path = Path(rules_dir)
    if not rules_path.exists():
        return []

    rules_files = []
    for file_path in rules_path.iterdir():
        if file_path.is_file() and file_path.suffix == '.md':
            file_size = file_path.stat().st_size
            rules_files.append((str(file_path), file_size))

    return rules_files


def interactive_rules_selection(rules_dir: str = "rules") -> List[str]:
    """
    Interactive selection of rules files with multi-select support.

    Returns:
        List of selected rules file paths, or empty list if none selected
    """
    files = list_rules_files(rules_dir)

    if not files:
        print(f"[INFO] No rules files found in '{rules_dir}' directory.")
        return []

    print(f"\n[INFO] Available rules files in '{rules_dir}':")
    for i, (file_path, file_size) in enumerate(files, 1):
        file_name = Path(file_path).name
        size_str = format_size(file_size)
        print(f"  {i}. {file_name} ({size_str})")

    print(f"\n[INPUT] Select rules files to use:")
    print(f"  - Enter numbers separated by commas (e.g., 1,3,5)")
    print(f"  - Enter 'all' to select all files")
    print(f"  - Enter 'none' or 'skip' to skip rules selection")
    print(f"  - Enter 'q' to quit")

    while True:
        try:
            choice = input(f"\n[PROMPT] Your choice: ").strip().lower()

            if choice in ['q', 'quit']:
                print("Goodbye!")
                sys.exit(0)

            if choice in ['none', 'skip', '']:
                print("[INFO] Skipping rules selection.")
                return []

            if choice == 'all':
                selected_files = [file_path for file_path, _ in files]
                print(f"[SUCCESS] Selected all {len(selected_files)} rules files.")
                return selected_files

            # Parse comma-separated numbers
            indices = []
            for part in choice.split(','):
                part = part.strip()
                if part.isdigit():
                    index = int(part) - 1
                    if 0 <= index < len(files):
                        indices.append(index)
                    else:
                        print(f"[ERROR] Invalid choice: {part}. Please enter numbers between 1 and {len(files)}.")
                        break
                else:
                    print(f"[ERROR] Invalid input: '{part}'. Please enter numbers, 'all', 'none', or 'q'.")
                    break
            else:
                # All parts were valid
                if indices:
                    selected_files = [files[i][0] for i in indices]
                    print(f"[SUCCESS] Selected {len(selected_files)} rules files:")
                    for file_path in selected_files:
                        print(f"   - {Path(file_path).name}")
                    return selected_files
                else:
                    print("[ERROR] No valid selections made.")

        except ValueError:
            print("[ERROR] Invalid input. Please enter numbers, 'all', 'none', or 'q'.")
        except KeyboardInterrupt:
            print("\nGoodbye!")
            sys.exit(0)


def load_prd_content(prd_path: Optional[str] = None, input_dir: str = "input") -> str:
    """Load PRD content from file, with interactive selection if path not provided."""
    # If no path provided, try interactive selection
    if prd_path is None:
        prd_path = interactive_file_selection(input_dir)
        if prd_path is None:
            sys.exit(1)

    try:
        with open(prd_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"[ERROR] PRD file not found: {prd_path}")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Error reading PRD file: {e}")
        sys.exit(1)


def clean_output_directory(output_dir: str = "output", force: bool = False, keep_latest: int = 0) -> None:
    """
    Clean the output directory with cross-platform compatibility.

    Args:
        output_dir: Output directory path
        force: Skip confirmation prompt
        keep_latest: Number of latest runs to keep
    """
    output_path = Path(output_dir)

    if not output_path.exists():
        print(f"[INFO] Output directory '{output_dir}' does not exist.")
        return

    # Get all run directories (timestamped directories)
    run_dirs = []
    for item in output_path.iterdir():
        if item.is_dir() and item.name.startswith("run_"):
            try:
                # Parse timestamp from directory name (run_YYYYMMDD_HHMMSS)
                timestamp_str = item.name[4:]  # Remove "run_" prefix
                timestamp = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
                run_dirs.append((timestamp, item))
            except ValueError:
                # If parsing fails, treat as regular directory
                run_dirs.append((datetime.min, item))

    if not run_dirs:
        print(f"[INFO] No run directories found in '{output_dir}'.")
        return

    # Sort by timestamp (newest first)
    run_dirs.sort(key=lambda x: x[0], reverse=True)

    # Determine which directories to remove
    if keep_latest > 0:
        dirs_to_remove = [item for _, item in run_dirs[keep_latest:]]
        dirs_to_keep = [item for _, item in run_dirs[:keep_latest]]
    else:
        dirs_to_remove = [item for _, item in run_dirs]
        dirs_to_keep = []

    if not dirs_to_remove:
        print(f"[INFO] No directories to clean (keeping latest {keep_latest} runs).")
        return

    # Show what will be cleaned
    print(f"[CLEAN] Clean Output Directory: {output_path.absolute()}")
    print(f"[INFO] Found {len(run_dirs)} run directories")

    if dirs_to_keep:
        print(f"[INFO] Keeping {len(dirs_to_keep)} latest runs:")
        for dir_path in dirs_to_keep:
            print(f"   - {dir_path.name}")

    print(f"[CLEAN] Will remove {len(dirs_to_remove)} directories:")
    for dir_path in dirs_to_remove:
        dir_size = get_directory_size(dir_path)
        print(f"   - {dir_path.name} ({format_size(dir_size)})")

    # Confirmation
    if not force:
        response = input(f"\n[CONFIRM] Are you sure you want to remove {len(dirs_to_remove)} directories? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("[INFO] Clean operation cancelled.")
            return

    # Remove directories
    removed_count = 0
    total_size_freed = 0

    for dir_path in dirs_to_remove:
        try:
            dir_size = get_directory_size(dir_path)

            # Use cross-platform removal
            if platform.system() == "Windows":
                # Use PowerShell Remove-Item for Windows
                subprocess.run([
                    "powershell", "-Command",
                    f"Remove-Item -Recurse -Force '{dir_path}'"
                ], check=True, capture_output=True)
            else:
                # Use shutil for Unix-like systems (macOS, Linux)
                shutil.rmtree(dir_path)

            removed_count += 1
            total_size_freed += dir_size
            print(f"[SUCCESS] Removed: {dir_path.name}")

        except Exception as e:
            print(f"[ERROR] Failed to remove {dir_path.name}: {e}")

    print(f"\n[SUCCESS] Clean completed!")
    print(f"[INFO] Removed {removed_count} directories")
    print(f"[INFO] Freed {format_size(total_size_freed)} of disk space")


def get_directory_size(path: Path) -> int:
    """Get the total size of a directory in bytes."""
    total_size = 0
    try:
        for item in path.rglob('*'):
            if item.is_file():
                total_size += item.stat().st_size
    except (OSError, PermissionError):
        pass
    return total_size


def format_size(size_bytes: int) -> str:
    """Format file size in human readable format."""
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    size = float(size_bytes)

    while size >= 1024.0 and i < len(size_names) - 1:
        size /= 1024.0
        i += 1

    return f"{size:.1f} {size_names[i]}"


def parse_module_list(modules_str: str) -> List[str]:
    """Parse comma-separated module list."""
    if not modules_str:
        return []
    return [m.strip() for m in modules_str.split(',') if m.strip()]


def create_orchestrator(verbose: bool = False, config_path: Optional[str] = None,
                       model_preset: Optional[str] = None, cli_args=None):
    """Create orchestrator instance with configuration."""
    if ORCHESTRATOR_CLASS is None:
        raise ImportError("Could not import orchestrator class")

    if ORCHESTRATOR_CLASS.__name__ == 'AIDevWorkflowOrchestrator':
        return ORCHESTRATOR_CLASS(
            llm=None,  # Will be created from config
            verbose=verbose,
            config_path=config_path,
            model_preset=model_preset,
            cli_args=cli_args
        )
    else:
        # Legacy Orchestrator - create with different signature
        config_manager = ConfigManager(config_path=config_path)
        llm_client = config_manager.create_llm()

        agents = {
            "business_analyzer": BusinessAnalyzerAgent(llm_client, verbose),
            "domain_modeler": DomainModelerAgent(llm_client, verbose),
            "requirements_analyzer": RequirementsAnalyzerAgent(llm_client, verbose),
            "technical_leader": TechnicalLeaderAgent(llm_client, verbose),
            "result_generator": ResultGeneratorAgent(llm_client, verbose),
            "presentation_generator": PresentationGeneratorAgent(llm_client, verbose)
        }

        return ORCHESTRATOR_CLASS(agents, verbose)


def cmd_full_workflow(args):
    """Execute full workflow command."""
    print("[START] Starting AI Development Workflow...")

    # Load PRD content (with interactive selection if no file specified)
    prd_file = getattr(args, 'prd_file', None)
    input_dir = getattr(args, 'input_dir', 'input')

    if prd_file:
        print(f"[INFO] PRD File: {prd_file}")
    else:
        print(f"[INFO] Selecting from input directory: {input_dir}")

    prd_content = load_prd_content(prd_file, input_dir)
    print(f"[INFO] PRD Content Length: {len(prd_content)} characters")
    print(f"[INFO] Output Directory: {args.output}")

    # Select rules files (command line or interactive selection)
    rules_dir = getattr(args, 'rules_dir', 'rules')
    rules_arg = getattr(args, 'rules', None)

    if rules_arg:
        # Parse command line rules specification
        specified_rules = [r.strip() for r in rules_arg.split(',') if r.strip()]
        selected_rules = []

        for rule_file in specified_rules:
            # Support both relative and absolute paths
            if not rule_file.startswith('/') and not rule_file.startswith('\\') and ':' not in rule_file:
                # Relative path - prepend rules directory
                rule_path = Path(rules_dir) / rule_file
            else:
                # Absolute path
                rule_path = Path(rule_file)

            if rule_path.exists():
                selected_rules.append(str(rule_path))
                print(f"[SUCCESS] Using rules file: {rule_path}")
            else:
                print(f"[ERROR] Rules file not found: {rule_path}")
                sys.exit(1)

        print(f"[INFO] Using {len(selected_rules)} specified rules files")
    else:
        # Interactive selection
        selected_rules = interactive_rules_selection(rules_dir)

        if selected_rules:
            print(f"[INFO] Selected {len(selected_rules)} rules files for processing")
        else:
            print("[INFO] No rules files selected - will use default rules")

    # Parse selected modules
    selected_modules = None
    if args.modules:
        selected_modules = parse_module_list(args.modules)
        print(f"[INFO] Selected Modules: {', '.join(selected_modules)}")
    
    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None),
        cli_args=args
    )

    # Display LLM information
    llm_info = orchestrator.get_llm_info()
    print(f"\n[LLM] LLM Configuration:")
    print(f"   - Provider: {llm_info['provider']}")
    print(f"   - Model: {llm_info['model']}")
    print(f"   - Temperature: {llm_info['temperature']}")
    print(f"   - Max Tokens: {llm_info['max_tokens']}")
    print(f"   - Streaming: {'Enabled' if llm_info['streaming'] else 'Disabled'}")
    print(f"   - Timeout: {llm_info['timeout']}s")
    
    # Execute workflow
    result = orchestrator.execute_full_workflow(
        prd_content=prd_content,
        project_root=args.project_root,
        output_dir=args.output,
        selected_modules=selected_modules,
        selected_rules=selected_rules
    )
    
    # Handle results
    if result.get("success"):
        print("\n[SUCCESS] Workflow completed successfully!")
        print(f"[INFO] Results saved to: {result['output_directory']}")
        print(f"[INFO] Summary: {result['summary_file']}")

        # Print module summary
        modules = result.get("modules", {})
        if modules:
            print(f"\n[MODULES] Processed Modules ({len(modules)}):")
            for module_name, module_data in modules.items():
                files = module_data.get("files", {})
                print(f"  - {module_name}")
                if "requirements" in files:
                    print(f"    - Requirements: {files['requirements']}")
                if "prompt" in files:
                    print(f"    - AI Prompt: {files['prompt']}")

        # Display LLM information in summary
        llm_info = orchestrator.get_llm_info()
        print(f"\n[LLM] LLM Configuration Used:")
        print(f"   - Provider: {llm_info['provider']}")
        print(f"   - Model: {llm_info['model']}")
        print(f"   - Temperature: {llm_info['temperature']}")
        print(f"   - Max Tokens: {llm_info['max_tokens']}")
        print(f"   - Streaming: {'Enabled' if llm_info['streaming'] else 'Disabled'}")
        print(f"   - Timeout: {llm_info['timeout']}s")
    else:
        print(f"\n[ERROR] Workflow failed: {result.get('error', 'Unknown error')}")
        if result.get("details"):
            for detail in result["details"]:
                print(f"   - {detail}")
        sys.exit(1)


def cmd_business_analysis(args):
    """Execute business analysis command."""
    print("[START] Running Business Analysis...")

    # Load PRD content (with interactive selection if no file specified)
    prd_file = getattr(args, 'prd_file', None)
    input_dir = getattr(args, 'input_dir', 'input')
    prd_content = load_prd_content(prd_file, input_dir)

    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None),
        cli_args=args
    )

    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute business analysis
    result = orchestrator._execute_business_analysis(prd_content)

    # Save results
    if result.success:
        output_file = args.output or "business_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)

        print(f"[SUCCESS] Business analysis completed!")
        print(f"[INFO] Results saved to: {output_file}")
        print(f"[INFO] Entities: {len(result.data.get('core_entities', []))}")
        print(f"[INFO] Requirements: {len(result.data.get('functional_requirements', []))}")
        print(f"[INFO] User Stories: {len(result.data.get('user_stories', []))}")
    else:
        print(f"[ERROR] Business analysis failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_domain_modeling(args):
    """Execute domain modeling command."""
    print("[START] Running Domain Modeling...")

    # Load business analysis
    try:
        with open(args.business_analysis, 'r', encoding='utf-8') as f:
            business_data = json.load(f)
    except Exception as e:
        print(f"[ERROR] Error loading business analysis: {e}")
        sys.exit(1)

    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None),
        cli_args=args
    )

    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute domain modeling
    result = orchestrator._execute_domain_modeling(business_data)

    # Save results
    if result.success:
        output_file = args.output or "domain_model.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)

        print(f"[SUCCESS] Domain modeling completed!")
        print(f"[INFO] Results saved to: {output_file}")
        print(f"[INFO] Bounded Contexts: {len(result.data.get('bounded_contexts', []))}")
        print(f"[INFO] Aggregates: {len(result.data.get('aggregates', []))}")
        print(f"[INFO] Entities: {len(result.data.get('domain_entities', []))}")
    else:
        print(f"[ERROR] Domain modeling failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_generate_requirements(args):
    """Execute requirements generation command."""
    print("[START] Generating Technical Requirements...")

    # Load domain model
    try:
        with open(args.domain_model, 'r', encoding='utf-8') as f:
            domain_data = json.load(f)
    except Exception as e:
        print(f"[ERROR] Error loading domain model: {e}")
        sys.exit(1)

    # Filter for specific module if provided
    if args.module:
        print(f"[INFO] Filtering for module: {args.module}")
        # TODO: Implement module filtering logic

    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None),
        cli_args=args
    )

    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute requirements generation
    result = orchestrator._execute_requirements_generation(domain_data)

    # Save results
    if result.success:
        output_file = args.output or f"{args.module or 'module'}_requirements.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result.data, f, indent=2, ensure_ascii=False, default=str)

        print(f"[SUCCESS] Requirements generation completed!")
        print(f"[INFO] Results saved to: {output_file}")

        # Print summary
        data = result.data
        print(f"[INFO] User Stories: {len(data.get('user_stories', []))}")
        print(f"[INFO] API Endpoints: {len(data.get('api_design', {}).get('endpoints', []))}")
        print(f"[INFO] Database Tables: {len(data.get('data_models', {}).get('tables', []))}")
    else:
        print(f"[ERROR] Requirements generation failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_build_prompt(args):
    """Execute prompt building command."""
    print("[START] Building AI Development Prompt...")

    # Load requirements
    try:
        with open(args.requirements, 'r', encoding='utf-8') as f:
            requirements_data = json.load(f)
    except Exception as e:
        print(f"[ERROR] Error loading requirements: {e}")
        sys.exit(1)

    # Create orchestrator with configuration
    orchestrator = create_orchestrator(
        verbose=args.verbose,
        config_path=getattr(args, 'config', None),
        model_preset=getattr(args, 'preset', None),
        cli_args=args
    )

    # Create context
    orchestrator.context = orchestrator.create_context(args.project_root)

    # Execute prompt building
    result = orchestrator._execute_prompt_building(requirements_data)

    # Save results
    if result.success:
        module_name = result.data.get("module_name", "module")
        output_file = args.output or f"{module_name}_ai_prompt.md"

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(result.data.get("prompt_content", ""))

        print(f"[SUCCESS] AI prompt building completed!")
        print(f"[INFO] Results saved to: {output_file}")
        print(f"[INFO] Word Count: {result.data.get('word_count', 0)}")
        print(f"[INFO] Estimated Tokens: {result.data.get('estimated_tokens', 0)}")
    else:
        print(f"[ERROR] Prompt building failed: {', '.join(result.errors)}")
        sys.exit(1)


def cmd_simple_workflow(args):
    """Execute simple workflow command (compatible with original main.py)."""
    print("[START] Starting Simple AI Development Workflow...")

    # Load PRD content
    prd_file = args.prd_file
    print(f"[INFO] PRD File: {prd_file}")

    try:
        with open(prd_file, 'r', encoding='utf-8') as f:
            prd_content = f.read()
    except Exception as e:
        print(f"[ERROR] Failed to read PRD file: {e}")
        sys.exit(1)

    print(f"[INFO] PRD Content Length: {len(prd_content)} characters")

    # Load rules content if provided
    rules_content = ""
    if args.rules and Path(args.rules).exists():
        try:
            with open(args.rules, 'r', encoding='utf-8') as f:
                rules_content = f.read()
            print(f"[INFO] Rules Content Length: {len(rules_content)} characters")
        except Exception as e:
            print(f"[WARNING] Failed to read rules file: {e}")
    else:
        print("[INFO] No rules file provided")

    # Create output directory
    if args.output:
        output_dir = Path(args.output)
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path("output") / f"run_{timestamp}"

    output_dir.mkdir(parents=True, exist_ok=True)
    print(f"[INFO] Output Directory: {output_dir}")

    # Initialize configuration and LLM
    try:
        config_path = args.config if args.config else "config.yaml"
        print(f"[INFO] Config File: {config_path}")

        config_manager = ConfigManager(config_path=config_path)
        llm_client = config_manager.create_llm()
        if llm_client is None:
            raise RuntimeError("Failed to create LLM client")

        # Create agents
        agents = {
            "business_analyzer": BusinessAnalyzerAgent(llm_client, args.verbose),
            "domain_modeler": DomainModelerAgent(llm_client, args.verbose),
            "requirements_analyzer": RequirementsAnalyzerAgent(llm_client, args.verbose),
            "technical_leader": TechnicalLeaderAgent(llm_client, args.verbose),
            "result_generator": ResultGeneratorAgent(llm_client, args.verbose),
            "presentation_generator": PresentationGeneratorAgent(llm_client, args.verbose)
        }

        # Create orchestrator
        orchestrator = Orchestrator(agents, args.verbose)

        # Execute workflow
        print("[INFO] Starting 6-step workflow execution...")
        results = orchestrator.execute_workflow(prd_content, rules_content, output_dir)

        # Print summary
        print("\n" + "=" * 60)
        print("AI开发工作流执行总结")
        print("=" * 60)

        print(f"执行状态: {'✅ 成功' if results['success'] else '❌ 失败'}")
        print(f"完成步骤: {results['steps_completed']}/{results['total_steps']}")
        print(f"执行时间: {results['execution_time']:.2f} 秒")

        if results['success']:
            workflow_results = results.get('results', {})
            business_analysis = workflow_results.get('business_analysis', {})
            if business_analysis:
                print(f"项目名称: {business_analysis.get('project_name', 'N/A')}")
                print(f"功能需求数量: {business_analysis.get('functional_requirements_count', 0)}")

        if results.get('errors'):
            print("执行错误:")
            for error in results['errors']:
                print(f"  - {error}")

        print("=" * 60)

        # Exit with appropriate code
        sys.exit(0 if results['success'] else 1)

    except Exception as e:
        print(f"[ERROR] Workflow execution failed: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


def cmd_clean(args):
    """Execute clean command."""
    print("[START] Cleaning output directory...")

    # Use default output directory if not specified
    output_dir = getattr(args, 'output', 'output')

    clean_output_directory(
        output_dir=output_dir,
        force=args.force,
        keep_latest=args.keep_latest
    )


def main_simple_workflow():
    """Handle simple workflow mode (compatible with original main.py)."""
    parser = argparse.ArgumentParser(
        description="AI开发工作流 - 6步业务分析与代码生成系统",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
工作流程:
  1. 业务分析 (Business Analysis)
  2. 领域建模 (Domain Modeling)
  3. 需求分析 (Requirements Analysis)
  4. 质量评审 (Quality Review)
  5. 结果生成 (Result Generation)
  6. HTML展示 (HTML Presentation)

示例用法:
  # 基本用法
  python main.py input/prd.md

  # 指定规则文件和详细输出
  python main.py input/prd.md --rules rules/rules.md --verbose

  # 自定义输出目录
  python main.py input/prd.md --output custom_output_dir

  # 指定配置文件
  python main.py input/prd.md --config config.yaml

  # 清理输出目录
  python main.py clean
  python main.py clean --keep-latest 3 --force
        """
    )

    # Check if this is a clean command
    if len(sys.argv) > 1 and sys.argv[1] == 'clean':
        parser.add_argument('command', choices=['clean'], help='Clean output directory')
        parser.add_argument("--force", "-f", action="store_true", help="Force clean without confirmation")
        parser.add_argument("--keep-latest", "-k", type=int, default=0, help="Keep latest N runs (default: 0 - remove all)")
        parser.add_argument("--output", default="output", help="Output directory to clean (default: output)")

        args = parser.parse_args()
        return cmd_clean(args)

    # Regular workflow arguments
    parser.add_argument("prd_file", type=Path, help="PRD文档文件路径")
    parser.add_argument("--rules", type=Path, help="项目规则文件路径（可选）")
    parser.add_argument("--output", type=Path, help="输出目录路径（默认自动生成时间戳目录）")
    parser.add_argument("--verbose", action="store_true", help="启用详细日志输出")
    parser.add_argument("--config", type=Path, default=None, help="LLM配置文件路径")

    args = parser.parse_args()
    return cmd_simple_workflow(args)


def main():
    """Main CLI entry point."""
    # Check if this is a simple workflow call (no subcommands)
    if len(sys.argv) > 1 and not sys.argv[1].startswith('-') and sys.argv[1] not in [
        'workflow', 'business-analysis', 'domain-modeling', 'generate-requirements',
        'build-prompt', 'clean'
    ]:
        # This is a simple workflow call with PRD file as first argument
        return main_simple_workflow()

    # Check for clean command without subcommand
    if len(sys.argv) > 1 and sys.argv[1] == 'clean':
        return main_simple_workflow()

    # Standard subcommand-based CLI
    parser = argparse.ArgumentParser(
        description="AI Development Workflow - Intelligent code generation from PRD to AI prompts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Simple workflow (compatible with original main.py)
  python main.py input/prd.md
  python main.py input/prd.md --rules rules/rules.md --verbose
  python main.py input/prd.md --output custom_output_dir

  # Advanced workflow with subcommands
  python main.py workflow
  python main.py workflow design/mcp-market-prd.txt

  # Step by step execution
  python main.py business-analysis
  python main.py domain-modeling business_analysis.json
  python main.py generate-requirements domain_model.json --module mcp_server
  python main.py build-prompt mcp_server_requirements.json

  # Clean output directory
  python main.py clean
  python main.py clean --keep-latest 3
  python main.py clean --force

Features:
  - Simple workflow mode (direct PRD file argument)
  - Interactive file selection from input directory
  - Context-aware rule loading for each agent type
  - Timestamped output directories with organized results
  - Module-based context storage for workflow steps
  - Cross-platform output directory cleaning
"""
    )
    
    # Global arguments
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], default="INFO", help="Set logging level (default: INFO)")
    parser.add_argument("--no-thinking", action="store_true", help="Disable thinking process display")
    parser.add_argument("--no-streaming", action="store_true", help="Disable streaming output")
    parser.add_argument("--project-root", default=".", help="Project root directory (default: current directory)")
    parser.add_argument("--config", "-c", help="Path to configuration file")
    parser.add_argument("--preset", "-p", help="Model preset to use (e.g., high_quality, creative)")
    parser.add_argument("--input-dir", default="input", help="Input directory for file selection (default: input)")

    # Subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Full workflow command
    workflow_parser = subparsers.add_parser("workflow", help="Execute full workflow")
    workflow_parser.add_argument("prd_file", nargs="?", help="Path to PRD file (optional - will prompt for selection if not provided)")
    workflow_parser.add_argument("--output", "-o", default="output", help="Base output directory (timestamped subdirectory will be created)")
    workflow_parser.add_argument("--modules", "-m", help="Comma-separated list of modules to process")
    workflow_parser.add_argument("--rules-dir", default="rules", help="Rules directory for file selection (default: rules)")
    workflow_parser.add_argument("--rules", "-r", help="Comma-separated list of specific rules files to use (skips interactive selection)")

    # Business analysis command
    business_parser = subparsers.add_parser("business-analysis", help="Analyze business requirements")
    business_parser.add_argument("prd_file", nargs="?", help="Path to PRD file (optional - will prompt for selection if not provided)")
    business_parser.add_argument("--output", "-o", help="Output file path")
    
    # Domain modeling command
    domain_parser = subparsers.add_parser("domain-modeling", help="Create domain models")
    domain_parser.add_argument("business_analysis", help="Path to business analysis JSON file")
    domain_parser.add_argument("--output", "-o", help="Output file path")
    
    # Requirements generation command
    req_parser = subparsers.add_parser("generate-requirements", help="Generate technical requirements")
    req_parser.add_argument("domain_model", help="Path to domain model JSON file")
    req_parser.add_argument("--module", help="Specific module to generate requirements for")
    req_parser.add_argument("--output", "-o", help="Output file path")
    
    # Prompt building command
    prompt_parser = subparsers.add_parser("build-prompt", help="Build AI development prompt")
    prompt_parser.add_argument("requirements", help="Path to requirements JSON file")
    prompt_parser.add_argument("--output", "-o", help="Output file path")

    # Clean command
    clean_parser = subparsers.add_parser("clean", help="Clean output directory")
    clean_parser.add_argument("--force", "-f", action="store_true", help="Force clean without confirmation")
    clean_parser.add_argument("--keep-latest", "-k", type=int, default=0, help="Keep latest N runs (default: 0 - remove all)")
    clean_parser.add_argument("--output", default="output", help="Output directory to clean (default: output)")

    # Parse arguments
    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    # Execute command
    try:
        if args.command == "workflow":
            cmd_full_workflow(args)
        elif args.command == "business-analysis":
            cmd_business_analysis(args)
        elif args.command == "domain-modeling":
            cmd_domain_modeling(args)
        elif args.command == "generate-requirements":
            cmd_generate_requirements(args)
        elif args.command == "build-prompt":
            cmd_build_prompt(args)
        elif args.command == "clean":
            cmd_clean(args)
        else:
            print(f"[ERROR] Unknown command: {args.command}")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n[WARNING] Operation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"[ERROR] Unexpected error: {e}")
        if getattr(args, 'verbose', False):
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
