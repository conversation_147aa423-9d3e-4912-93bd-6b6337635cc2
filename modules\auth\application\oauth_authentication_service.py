"""OAuth authentication service for handling OAuth login flows."""

import uuid
from datetime import datetime

from modules.auth.application.credential_auth_service import CredentialAuthService
from modules.auth.domain.credential_auth_repositories import UserRepository
from modules.auth.domain.oauth_account_models import OAuthAccount
from modules.auth.domain.oauth_account_repositories import OAuthAccountRepository
from modules.user.domain.user_models import User, UserStatus
from shared.oauth.client_service import OAuthClientService


class OAuthAuthenticationService:
    """Service for handling OAuth authentication flows."""

    def __init__(
        self,
        user_repository: UserRepository,
        oauth_account_repository: OAuthAccountRepository,
        oauth_client_service: OAuthClientService,
        credential_auth_service: CredentialAuthService,
    ):
        self.user_repository = user_repository
        self.oauth_account_repository = oauth_account_repository
        self.oauth_client_service = oauth_client_service
        self.credential_auth_service = credential_auth_service

    async def get_authorization_url(
        self, provider: str, state: str | None = None
    ) -> str:
        """Generate OAuth authorization URL for the specified provider."""
        return await self.oauth_client_service.generate_authorization_url(
            provider, state
        )

    async def handle_oauth_callback(self, provider: str, code: str) -> tuple[User, str]:
        """Handle OAuth callback and return user with access token."""
        # Complete OAuth flow to get user info (this stays async for HTTP calls)
        oauth_result = await self.oauth_client_service.handle_oauth_flow(provider, code)
        user_info = oauth_result["user_info"]

        # Check if OAuth account already exists
        oauth_account = self.oauth_account_repository.get_by_provider_and_id(
            provider, user_info["oauth_id"]
        )

        if oauth_account:
            # Existing OAuth account - get associated user
            user = self.user_repository.get_by_id(oauth_account.user_id)
            if not user:
                raise ValueError("Associated user not found")

            # Check if user is active
            if user.status != UserStatus.ACTIVE:
                raise ValueError(f"User account is {user.status.value}")
        else:
            # New OAuth account - create or link user
            user = await self._create_or_link_user(provider, user_info)

            # Create OAuth account link
            oauth_account = OAuthAccount(
                id=uuid.uuid4(),
                user_id=user.id,
                provider=provider,
                provider_account_id=user_info["oauth_id"],
                created_at=datetime.now(),
            )
            self.oauth_account_repository.create(oauth_account)

        # Generate access token
        access_token = self.credential_auth_service.create_access_token(user)

        return user, access_token

    async def _create_or_link_user(self, provider: str, user_info: dict) -> User:
        """Create new user or link to existing user based on email."""
        email = user_info.get("email")

        # Try to find existing user by email if available
        existing_user = None
        if email:
            existing_user = self.user_repository.get_by_email(email)

        if existing_user:
            # Link to existing user
            return existing_user
        else:
            # Create new user
            username = await self._generate_unique_username(user_info["username"])

            now = datetime.now()
            user = User(
                id=uuid.uuid4(),
                username=username,
                email=email,
                hashed_password=None,  # OAuth users don't have passwords
                status=UserStatus.ACTIVE,
                created_at=now,
                updated_at=now,
            )

            return self.user_repository.create(user)

    async def _generate_unique_username(self, preferred_username: str) -> str:
        """Generate a unique username based on preferred username."""
        base_username = preferred_username
        counter = 0

        while True:
            candidate = base_username if counter == 0 else f"{base_username}_{counter}"

            if not self.user_repository.exists_by_username(candidate):
                return candidate

            counter += 1

            # Prevent infinite loop
            if counter > 1000:
                # Fallback to UUID-based username
                return f"{base_username}_{uuid.uuid4().hex[:8]}"

    def get_user_oauth_accounts(self, user_id: uuid.UUID) -> list[OAuthAccount]:
        """Get all OAuth accounts for a user."""
        return self.oauth_account_repository.get_by_user_id(user_id)

    def unlink_oauth_account(
        self, user_id: uuid.UUID, oauth_account_id: uuid.UUID
    ) -> bool:
        """Unlink an OAuth account from a user."""
        # Verify the OAuth account belongs to the user
        oauth_accounts = self.oauth_account_repository.get_by_user_id(user_id)
        oauth_account = next(
            (acc for acc in oauth_accounts if acc.id == oauth_account_id), None
        )

        if not oauth_account:
            return False

        # Check if user has other authentication methods
        user = self.user_repository.get_by_id(user_id)
        if not user:
            return False

        # If user has no password and this is their only OAuth account, prevent unlinking
        if not user.hashed_password and len(oauth_accounts) == 1:
            raise ValueError(
                "Cannot unlink the only authentication method for this user"
            )

        return self.oauth_account_repository.delete(oauth_account_id)

    async def get_available_providers(self) -> list[dict]:
        """Get list of available OAuth providers."""
        providers = await self.oauth_client_service.get_all_enabled_providers()

        return [
            {
                "name": provider.name,
                "display_name": provider.display_name,
                "description": provider.description,
                "icon_url": str(provider.icon_url) if provider.icon_url else None,
            }
            for provider in providers
        ]
