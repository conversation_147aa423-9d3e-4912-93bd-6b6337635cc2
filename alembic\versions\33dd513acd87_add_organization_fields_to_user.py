"""Add organization fields to user

Revision ID: 33dd513acd87
Revises: e2552f225b03
Create Date: 2025-06-21 18:52:03.028715

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '33dd513acd87'
down_revision = 'e2552f225b03'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('organization_id', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('department', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('role', sa.String(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_column('role')
        batch_op.drop_column('department')
        batch_op.drop_column('organization_id')

    # ### end Alembic commands ###
