#!/usr/bin/env python3
"""
Simple test for BusinessAnalyzerAgent
"""

import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from agents.business_analyzer import BusinessAnalyzerAgent
from core.base_agent import WorkflowContext

def test_simple_business_analyzer():
    """Test the business analyzer with mock LLM."""
    print("=== Testing Business Analyzer (Mock Mode) ===")
    
    # Initialize agent without LLM (will use mock)
    agent = BusinessAnalyzerAgent(llm=None, verbose=True)
    
    # Create workflow context
    workflow_context = WorkflowContext(
        project_root=".",
        project_rules={},
        existing_modules=["auth", "user"],
        tech_stack=["FastAPI", "SQLAlchemy"],
        architecture_style="DDD"
    )
    
    # Test PRD content
    test_prd = """
# MCP Server Market Platform

## 项目概述
MCP Server Market Platform 是一个集中化的 Model Context Protocol 服务器市场平台。

## 核心功能
1. 服务器发现 - 用户可以浏览和搜索 MCP 服务器
2. 服务器提交 - 作者可以提交自己的服务器
3. 用户管理 - 支持用户注册和认证
4. 评价系统 - 用户可以评价服务器

## 业务规则
- 只有认证用户才能提交服务器
- 服务器必须通过验证才能发布
"""
    
    # Test basic analysis
    print("\n--- 基础分析测试 ---")
    input_data = {"prd_content": test_prd}
    
    try:
        result = agent.process(input_data, workflow_context)
        
        if result.success:
            print("✓ 分析成功")
            print(f"  执行时间: {result.execution_time:.2f}s")
            
            # Print data summary
            data = result.data
            print(f"  业务实体数量: {len(data.get('core_entities', []))}")
            print(f"  功能需求数量: {len(data.get('functional_requirements', []))}")
            print(f"  用户故事数量: {len(data.get('user_stories', []))}")
            
            # Print some sample data
            entities = data.get('core_entities', [])
            if entities:
                print(f"  示例实体: {entities[0].get('name', 'N/A')}")
            
            requirements = data.get('functional_requirements', [])
            if requirements:
                print(f"  示例需求: {requirements[0].get('title', 'N/A')}")
                
        else:
            print("✗ 分析失败")
            print(f"  错误: {result.errors}")
            
    except Exception as e:
        print(f"✗ 测试异常: {e}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_simple_business_analyzer()
