"""Auth module dependency injection."""

import os

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTP<PERSON>earer
from sqlalchemy.orm import Session

from common.db.database import get_db
from modules.auth.application.credential_auth_service import CredentialAuthService
from modules.auth.application.oauth_authentication_service import (
    OAuthAuthenticationService,
)
from modules.auth.domain.credential_auth_repositories import UserRepository
from modules.auth.domain.oauth_account_repositories import OAuthAccountRepository
from modules.auth.infrastructure.credential_auth_repositories import UserRepositoryImpl
from modules.auth.infrastructure.oauth_account_repositories import (
    OAuthAccountRepositoryImpl,
)
from modules.oauth_provider.domain.oauth_provider_repositories import (
    OAuthProviderRepository,
)
from modules.oauth_provider.infrastructure.oauth_provider_repositories import (
    OAuthProviderRepositoryImpl,
)
from modules.user.domain.user_models import User
from shared.oauth.client_service import OAuthClientService


def get_user_repository(
    session: Session = Depends(get_db),
) -> UserRepository:
    """Dependency to get user repository."""
    return UserRepositoryImpl(session)


def get_oauth_account_repository(
    session: Session = Depends(get_db),
) -> OAuthAccountRepository:
    """Dependency to get OAuth account repository."""
    return OAuthAccountRepositoryImpl(session)


def get_oauth_provider_repository(
    session: Session = Depends(get_db),
) -> OAuthProviderRepository:
    """Dependency to get OAuth provider repository."""
    return OAuthProviderRepositoryImpl(session)


def get_oauth_client_service(
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> OAuthClientService:
    """Dependency to get shared OAuth client service."""
    oauth_base_url = os.getenv("AI4SE_MCP_HUB_OAUTH_BASE_URL", "http://localhost:8000")
    return OAuthClientService(oauth_provider_repo, oauth_base_url)


def get_credential_auth_service(
    user_repository: UserRepository = Depends(get_user_repository),
) -> CredentialAuthService:
    """Dependency to get credential authentication service."""
    secret_key = os.getenv("AI4SE_MCP_HUB_SECRET_KEY", "your-secret-key-here")
    return CredentialAuthService(
        user_repository=user_repository,
        secret_key=secret_key,
        algorithm="HS256",
        token_expire_minutes=30,
    )


def get_oauth_authentication_service(
    user_repository: UserRepository = Depends(get_user_repository),
    oauth_account_repository: OAuthAccountRepository = Depends(
        get_oauth_account_repository
    ),
    oauth_client_service: OAuthClientService = Depends(get_oauth_client_service),
    credential_auth_service: CredentialAuthService = Depends(
        get_credential_auth_service
    ),
) -> OAuthAuthenticationService:
    """Dependency to get OAuth authentication service."""
    return OAuthAuthenticationService(
        user_repository=user_repository,
        oauth_account_repository=oauth_account_repository,
        oauth_client_service=oauth_client_service,
        credential_auth_service=credential_auth_service,
    )


# JWT token verification
security = HTTPBearer()


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    credential_auth_service: CredentialAuthService = Depends(
        get_credential_auth_service
    ),
) -> User:
    """Verify JWT token and return current user."""
    try:
        payload = credential_auth_service.verify_token(credentials.credentials)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        user_id = payload.get("sub")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Could not validate credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except jwt.PyJWTError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        ) from e

    user = credential_auth_service.get_user_by_id(user_id)
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return user
