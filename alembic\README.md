# Alembic 数据库迁移工具使用指南

## 基本命令

1. 初始化迁移环境 (已初始化可跳过):
   ```bash
   alembic init alembic
   ```

2. 创建新迁移脚本:
   ```bash 
   alembic revision -m "迁移描述"
   ```

3. 自动生成迁移脚本(基于模型变更):
   ```bash
   alembic revision -m "your_message" --autogenerate
   ```

4. 执行迁移到最新版本:
   ```bash
   alembic upgrade head
   ```

5. 降级到指定版本:
   ```bash
   alembic downgrade 版本号
   ```

6. 查看当前版本:
   ```bash
   alembic current
   ```

7. 查看历史记录:
   ```bash
   alembic history
   ```

## 项目配置说明

1. 数据库连接配置在 `alembic.ini` 文件中:
   ```ini
   sqlalchemy.url = driver://user:pass@localhost/dbname
   ```

2. 迁移脚本目录结构:
   - `versions/` - 存放迁移脚本
   - `env.py` - 迁移环境配置
   - `script.py.mako` - 迁移脚本模板

## SQL操作示例

### 创建表
```python
def upgrade():
    op.execute("""
        CREATE TABLE users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(50) NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL
        )
    """)
```

### 修改表结构
```python
def upgrade():
    op.execute("ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT TRUE")
```

### 数据迁移
```python
def upgrade():
    op.execute("""
        INSERT INTO new_table (col1, col2)
        SELECT old_col1, old_col2 FROM old_table
    """)
```

### 回滚操作
```python
def downgrade():
    op.execute("DROP TABLE users")
```

## 最佳实践

1. 每次模型变更后都应创建新迁移脚本
2. 迁移脚本应包含完整的升级(upgrade)和降级(downgrade)逻辑
3. 测试环境应先执行 `alembic upgrade head` 确保数据库最新
4. 生产环境部署时应验证迁移脚本
5. 复杂的迁移建议拆分为多个小迁移

## 注意事项

1. 不要手动修改已提交的迁移脚本
2. 确保迁移脚本在测试环境验证通过后再部署
3. 团队开发时应同步迁移脚本
4. 确保SQL语法与目标数据库兼容
5. 生产环境执行前先在测试环境验证