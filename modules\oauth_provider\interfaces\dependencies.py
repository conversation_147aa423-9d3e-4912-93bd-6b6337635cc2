"""OAuth Provider module dependency injection."""

from fastapi import Depends
from sqlalchemy.orm import Session

from common.db.database import get_db
from modules.oauth_provider.application.oauth_provider_service import (
    OAuthProviderService,
)
from modules.oauth_provider.domain.oauth_provider_repositories import (
    OAuthProviderRepository,
)
from modules.oauth_provider.infrastructure.oauth_provider_repositories import (
    OAuthProviderRepositoryImpl,
)


def get_oauth_provider_repository(
    session: Session = Depends(get_db),
) -> OAuthProviderRepository:
    """Dependency to get OAuth provider repository."""
    return OAuthProviderRepositoryImpl(session)


def get_oauth_provider_service(
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> OAuthProviderService:
    """Dependency to get OAuth provider service."""
    return OAuthProviderService(oauth_provider_repo)
