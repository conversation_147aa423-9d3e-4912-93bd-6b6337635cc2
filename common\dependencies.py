import os
from collections.abc import Generator
from functools import lru_cache

from pydantic_settings import BaseSettings
from sqlalchemy.orm import Session

from common.db.database import SessionLocal


# 基础配置模型
class AppConfig(BaseSettings):
    jwt_secret: str = os.getenv("AI4SE_MCP_HUB_JWT_SECRET", "default-secret")
    jwt_algorithm: str = "HS256"
    token_expire_minutes: int = 30

    # OAuth Configuration
    github_client_id: str = os.getenv("AI4SE_MCP_HUB_GITHUB_CLIENT_ID", "")
    github_client_secret: str = os.getenv("AI4SE_MCP_HUB_GITHUB_CLIENT_SECRET", "")
    google_client_id: str = os.getenv("AI4SE_MCP_HUB_GOOGLE_CLIENT_ID", "")
    google_client_secret: str = os.getenv("AI4SE_MCP_HUB_GOOGLE_CLIENT_SECRET", "")

    # Base URL for OAuth callbacks (without provider-specific path)
    oauth_base_url: str = os.getenv(
        "AI4SE_MCP_HUB_OAUTH_BASE_URL", "http://localhost:8000"
    )

    def get_oauth_redirect_uri(self, provider: str) -> str:
        """Generate OAuth redirect URI for the specified provider."""
        return f"{self.oauth_base_url}/api/v1/auth/oauth/{provider}/callback"


# 公共依赖项
@lru_cache
def get_config() -> AppConfig:
    return AppConfig()


def get_db() -> Generator[Session, None, None]:
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
