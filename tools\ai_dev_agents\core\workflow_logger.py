"""
Workflow Logger for detailed logging of AI development workflow execution.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional


class WorkflowLogger:
    """Enhanced logger for workflow execution tracking."""
    
    def __init__(self, output_path: Path, verbose: bool = False):
        """Initialize workflow logger."""
        self.output_path = output_path
        self.verbose = verbose
        self.log_file = output_path / "workflow_execution.log"
        self.json_log_file = output_path / "workflow_execution.json"
        
        # Create output directory
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Setup file logger
        self.logger = logging.getLogger("workflow_execution")
        self.logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        
        # File handler
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO if verbose else logging.WARNING)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # JSON log data
        self.json_log_data = {
            "workflow_start": datetime.now().isoformat(),
            "steps": [],
            "summary": {}
        }
    
    def log_step_start(self, step_number: int, step_name: str, input_data: Dict[str, Any] = None):
        """Log the start of a workflow step."""
        step_data = {
            "step_number": step_number,
            "step_name": step_name,
            "start_time": datetime.now().isoformat(),
            "status": "started",
            "input_summary": self._summarize_data(input_data) if input_data else {},
            "output_summary": {},
            "errors": [],
            "duration_seconds": 0
        }
        
        self.json_log_data["steps"].append(step_data)
        
        self.logger.info(f"=== STEP {step_number}: {step_name} STARTED ===")
        if input_data and self.verbose:
            self.logger.debug(f"Input data summary: {self._summarize_data(input_data)}")
    
    def log_step_progress(self, step_number: int, message: str, data: Dict[str, Any] = None):
        """Log progress within a step."""
        self.logger.info(f"Step {step_number} Progress: {message}")
        if data and self.verbose:
            self.logger.debug(f"Progress data: {self._summarize_data(data)}")
    
    def log_step_complete(self, step_number: int, output_data: Dict[str, Any] = None, 
                         errors: List[str] = None):
        """Log the completion of a workflow step."""
        if step_number <= len(self.json_log_data["steps"]):
            step_data = self.json_log_data["steps"][step_number - 1]
            step_data["status"] = "completed" if not errors else "failed"
            step_data["end_time"] = datetime.now().isoformat()
            step_data["output_summary"] = self._summarize_data(output_data) if output_data else {}
            step_data["errors"] = errors or []
            
            # Calculate duration
            start_time = datetime.fromisoformat(step_data["start_time"])
            end_time = datetime.fromisoformat(step_data["end_time"])
            step_data["duration_seconds"] = (end_time - start_time).total_seconds()
        
        status = "COMPLETED" if not errors else "FAILED"
        self.logger.info(f"=== STEP {step_number}: {status} ===")
        
        if output_data and self.verbose:
            self.logger.debug(f"Output data summary: {self._summarize_data(output_data)}")
        
        if errors:
            for error in errors:
                self.logger.error(f"Step {step_number} Error: {error}")
    
    def log_llm_interaction(self, step_number: int, agent_name: str, 
                           prompt_summary: str, response_summary: str,
                           duration_seconds: float = 0):
        """Log LLM interaction details."""
        interaction_data = {
            "timestamp": datetime.now().isoformat(),
            "agent_name": agent_name,
            "prompt_summary": prompt_summary,
            "response_summary": response_summary,
            "duration_seconds": duration_seconds
        }
        
        # Add to current step
        if step_number <= len(self.json_log_data["steps"]):
            step_data = self.json_log_data["steps"][step_number - 1]
            if "llm_interactions" not in step_data:
                step_data["llm_interactions"] = []
            step_data["llm_interactions"].append(interaction_data)
        
        self.logger.info(f"LLM Interaction [{agent_name}]: {prompt_summary} -> {response_summary}")
        if duration_seconds > 0:
            self.logger.debug(f"LLM call duration: {duration_seconds:.2f} seconds")
    
    def log_file_operation(self, step_number: int, operation: str, file_path: str, 
                          content_summary: str = ""):
        """Log file operations."""
        file_op_data = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "file_path": file_path,
            "content_summary": content_summary
        }
        
        # Add to current step
        if step_number <= len(self.json_log_data["steps"]):
            step_data = self.json_log_data["steps"][step_number - 1]
            if "file_operations" not in step_data:
                step_data["file_operations"] = []
            step_data["file_operations"].append(file_op_data)
        
        self.logger.info(f"File {operation}: {file_path}")
        if content_summary:
            self.logger.debug(f"Content summary: {content_summary}")
    
    def log_workflow_complete(self, success: bool, summary_data: Dict[str, Any]):
        """Log workflow completion."""
        self.json_log_data["workflow_end"] = datetime.now().isoformat()
        self.json_log_data["success"] = success
        self.json_log_data["summary"] = summary_data
        
        # Calculate total duration
        start_time = datetime.fromisoformat(self.json_log_data["workflow_start"])
        end_time = datetime.fromisoformat(self.json_log_data["workflow_end"])
        self.json_log_data["total_duration_seconds"] = (end_time - start_time).total_seconds()
        
        # Save JSON log
        self._save_json_log()
        
        status = "COMPLETED SUCCESSFULLY" if success else "FAILED"
        self.logger.info(f"=== WORKFLOW {status} ===")
        self.logger.info(f"Total duration: {self.json_log_data['total_duration_seconds']:.2f} seconds")
        self.logger.info(f"Detailed logs saved to: {self.log_file}")
        self.logger.info(f"JSON log saved to: {self.json_log_file}")
    
    def _summarize_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a summary of data for logging."""
        if not data:
            return {}
        
        summary = {}
        for key, value in data.items():
            if isinstance(value, str):
                summary[key] = f"String({len(value)} chars)"
            elif isinstance(value, list):
                summary[key] = f"List({len(value)} items)"
            elif isinstance(value, dict):
                summary[key] = f"Dict({len(value)} keys)"
            else:
                summary[key] = str(type(value).__name__)
        
        return summary
    
    def _save_json_log(self):
        """Save JSON log data to file."""
        try:
            with open(self.json_log_file, 'w', encoding='utf-8') as f:
                json.dump(self.json_log_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Failed to save JSON log: {e}")
    
    def get_step_summary(self, step_number: int) -> Optional[Dict[str, Any]]:
        """Get summary of a specific step."""
        if step_number <= len(self.json_log_data["steps"]):
            return self.json_log_data["steps"][step_number - 1]
        return None
    
    def get_workflow_summary(self) -> Dict[str, Any]:
        """Get overall workflow summary."""
        return {
            "total_steps": len(self.json_log_data["steps"]),
            "completed_steps": len([s for s in self.json_log_data["steps"] if s["status"] == "completed"]),
            "failed_steps": len([s for s in self.json_log_data["steps"] if s["status"] == "failed"]),
            "total_duration": self.json_log_data.get("total_duration_seconds", 0),
            "success": self.json_log_data.get("success", False)
        }
