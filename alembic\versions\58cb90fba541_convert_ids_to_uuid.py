"""convert_ids_to_uuid

Revision ID: 58cb90fba541
Revises: 61b8ee2fe814
Create Date: 2025-06-24 10:11:39.606045

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = '58cb90fba541'
down_revision = '61b8ee2fe814'
branch_labels = None
depends_on = None


def upgrade():
    """Convert integer IDs to UUIDs with data preservation."""

    # Step 1: Add UUID extension if not exists
    op.execute("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"")

    # Step 2: Add temporary UUID columns
    op.add_column('users', sa.Column('id_uuid', UUID(as_uuid=True), nullable=True))
    op.add_column('oauth_accounts', sa.Column('id_uuid', UUID(as_uuid=True), nullable=True))
    op.add_column('oauth_accounts', sa.Column('user_id_uuid', UUID(as_uuid=True), nullable=True))

    # Step 3: Generate UUIDs for existing data
    op.execute("UPDATE users SET id_uuid = uuid_generate_v4()")
    op.execute("UPDATE oauth_accounts SET id_uuid = uuid_generate_v4()")

    # Step 4: Update foreign key relationships
    op.execute("""
        UPDATE oauth_accounts
        SET user_id_uuid = users.id_uuid
        FROM users
        WHERE oauth_accounts.user_id = users.id
    """)

    # Step 5: Drop old foreign key constraint
    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.drop_constraint('oauth_accounts_user_id_fkey', type_='foreignkey')
        batch_op.drop_index('ix_oauth_accounts_id')

    # Step 6: Drop old columns and rename UUID columns
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index('ix_users_id')
        batch_op.drop_column('id')
        batch_op.alter_column('id_uuid', new_column_name='id', nullable=False)

    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.drop_column('user_id')
        batch_op.drop_column('id')
        batch_op.alter_column('id_uuid', new_column_name='id', nullable=False)
        batch_op.alter_column('user_id_uuid', new_column_name='user_id', nullable=False)

    # Step 7: Recreate primary keys and foreign keys
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_primary_key('users_pkey', ['id'])

    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.create_primary_key('oauth_accounts_pkey', ['id'])
        batch_op.create_foreign_key('oauth_accounts_user_id_fkey', 'users', ['user_id'], ['id'])


def downgrade():
    """Downgrade is not supported for UUID to integer conversion due to data loss."""
    raise NotImplementedError("Downgrade from UUID to integer is not supported due to potential data loss.")
