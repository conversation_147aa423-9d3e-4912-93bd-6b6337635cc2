# OAuth 故障排除指南

## 问题总结

在测试 GitHub OAuth 完整流程时，我们遇到了几个关键问题并成功解决：

### 1. 配置缓存问题
**问题**: OAuth redirect URI 配置被缓存，导致修改后的配置没有生效。
**解决方案**: 清理了 `AppConfig` 类中的重复字段定义，移除了废弃的配置项。

### 2. 邮箱字段验证错误
**问题**: GitHub 用户可能没有公开邮箱，导致 `email` 字段为 `None`，但 Pydantic 验证失败。
**错误信息**: `value is not a valid email address: An email address must have an @-sign.`

**解决方案**:
- 修改 `User` 模型，使 `email` 字段可选：`email: EmailStr | None = None`
- 更新数据库 ORM 模型，允许 `email` 和 `hashed_password` 为 `NULL`
- 创建数据库迁移来更新表结构
- 改进 OAuth 数据标准化逻辑，正确处理空邮箱

### 3. 用户名唯一约束冲突
**问题**: 当 OAuth 账户记录丢失但用户已存在时，尝试创建重复用户名导致数据库约束违反。
**错误信息**: `duplicate key value violates unique constraint "users_username_key"`

**解决方案**:
- 添加智能用户查找逻辑：先通过邮箱查找，再通过预期的 OAuth 用户名查找
- 实现用户名冲突处理：如果用户名已存在，自动生成唯一的用户名
- 优化 OAuth 账户关联逻辑

## 修复的代码变更

### 1. 用户模型更新
```python
# modules/user/domain/models.py
class User(BaseModel):
    id: UUID
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr | None = None  # 改为可选
    hashed_password: str | None = None
    status: UserStatus = Field(default=UserStatus.ACTIVE)
```

### 2. OAuth 数据标准化改进
```python
# modules/auth/application/oauth_client_service.py
def _normalize_user_data(self, provider: OAuthProvider, raw_data: dict[str, Any]):
    if provider == OAuthProvider.GITHUB:
        # 处理 GitHub 可能返回的各种空值情况
        email = raw_data.get("email")
        if not email or email == "None" or email.strip() == "":
            email = None
        
        return {
            "oauth_id": str(raw_data["id"]),
            "email": email,
            "name": raw_data.get("name"),
            "username": raw_data.get("login"),
            "avatar_url": raw_data.get("avatar_url"),
        }
```

### 3. 智能用户查找和创建逻辑
```python
# modules/auth/application/services.py
async def handle_oauth_callback(self, provider: str, oauth_id: str, email: str | None, ...):
    # 1. 首先检查 OAuth 账户是否存在
    oauth_account = await self.oauth_account_repo.get_by_provider_and_id(provider, oauth_id)
    
    if oauth_account:
        # OAuth 账户存在，直接获取关联用户
        user = await self.user_service.get_user_by_id(oauth_account.user_id)
    else:
        # 2. 通过邮箱查找用户
        user = None
        if email:
            user = await self.user_service.get_user_by_email(email)
        
        # 3. 通过预期的 OAuth 用户名查找用户
        if not user:
            expected_username = f"{provider}_{oauth_id}"
            user = await self.user_service.get_user_by_username(expected_username)
        
        # 4. 创建新用户（如果仍未找到）
        if not user:
            # 生成唯一用户名
            username = await self._generate_unique_username(email, provider, oauth_id)
            user = await self.user_service.create_user(new_user)
        
        # 5. 创建 OAuth 账户关联
        await self.oauth_account_repo.create(new_oauth)
```

## 测试验证

所有修复都通过了以下测试：
- ✅ OAuth 授权 URL 生成
- ✅ GitHub 回调处理（有邮箱）
- ✅ GitHub 回调处理（无邮箱）
- ✅ 用户名冲突处理
- ✅ 重复 OAuth 登录

## 最佳实践建议

1. **邮箱隐私**: 始终假设 OAuth 用户可能没有公开邮箱
2. **用户名生成**: 使用 `{provider}_{oauth_id}` 格式确保唯一性
3. **错误处理**: 实现优雅的降级策略处理各种边缘情况
4. **数据库约束**: 正确处理唯一约束，避免应用层错误
5. **测试覆盖**: 为所有 OAuth 流程编写全面的测试用例

## 最终修复总结

### 4. 字符串 "None" 验证错误 (最新修复)
**问题**: GitHub API 可能返回字符串 `"None"` 而不是 Python `None`，导致 Pydantic 验证失败。
**错误信息**: `value is not a valid email address: An email address must have an @-sign. [type=value_error, input_value='None', input_type=str]`

**解决方案**:
- 在 User 模型中添加自定义 email 验证器
- 强化 OAuth 数据标准化逻辑，处理更多边缘情况
- 确保所有 "None" 的字符串表示都被正确转换为 Python `None`

```python
# modules/user/domain/models.py
@field_validator('email', mode='before')
@classmethod
def validate_email(cls, v: Any) -> str | None:
    """Validate email field, converting various 'None' representations to actual None"""
    if v is None:
        return None

    str_v = str(v).strip()
    if (
        str_v == ""
        or str_v.lower() == "none"
        or str_v.lower() == "null"
        or str_v == "None"
        or str_v == "NULL"
    ):
        return None

    return v
```

## 测试用例覆盖

现在的测试用例覆盖了所有异常场景：
- ✅ OAuth URL 生成 (GitHub/Google)
- ✅ 有效回调处理
- ✅ 无效授权码处理
- ✅ 无公开邮箱的 GitHub 用户
- ✅ 用户名冲突处理
- ✅ 邮箱验证错误处理
- ✅ 数据库约束违反处理

## GitHub OAuth 特殊注意事项

根据 [GitHub OAuth 文档](https://docs.github.com/en/apps/oauth-apps/building-oauth-apps/authorizing-oauth-apps)：

- 用户可以选择不公开邮箱地址
- `user:email` scope 只能访问用户选择公开的邮箱
- 应该使用 `login` 字段作为唯一标识符
- OAuth ID (`id` 字段) 是永久不变的唯一标识符

## Google OAuth 特定问题修复

### 5. OAuth 代码交换参数错误
**问题**: Google OAuth 回调时出现 `Missing parameter: redirect_uri` 错误。
**原因**: 使用了错误的 `authorization_response` 参数而不是明确的 `code` 和 `redirect_uri` 参数。

**解决方案**:
```python
# 修复前
token = await oauth_client.fetch_token(
    config["token_url"],
    authorization_response=f"{redirect_uri}?code={code}",
)

# 修复后
token = await oauth_client.fetch_token(
    config["token_url"],
    code=code,
    redirect_uri=redirect_uri,
)
```

### 6. 短用户名验证错误
**问题**: Google 用户邮箱前缀可能很短（如 `<EMAIL>`），导致用户名不满足最小长度要求。
**错误信息**: `String should have at least 3 characters [type=string_too_short, input_value='me']`

**解决方案**:
```python
# 智能用户名生成逻辑
if email:
    email_prefix = email.split("@")[0]
    if len(email_prefix) >= 3:
        username = email_prefix
    else:
        # 如果邮箱前缀太短，使用 provider_oauth_id 格式
        username = f"{provider}_{oauth_id}"
```

## 完整测试流程

### GitHub OAuth
1. **生成授权 URL**: `GET /api/v1/auth/oauth/authorize/github`
2. **用户授权**: 在 GitHub 完成授权
3. **处理回调**: `GET /api/v1/auth/oauth/github/callback?code=...`
4. **返回 JWT**: 成功返回访问令牌

### Google OAuth
1. **生成授权 URL**: `GET /api/v1/auth/oauth/authorize/google`
2. **用户授权**: 在 Google 完成授权
3. **处理回调**: `GET /api/v1/auth/oauth/google/callback?code=...`
4. **返回 JWT**: 成功返回访问令牌

系统现在能够处理所有已知的边缘情况和异常场景，支持 GitHub 和 Google 两个 OAuth 提供商。
