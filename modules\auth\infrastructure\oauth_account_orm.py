"""OAuth account ORM models."""

import uuid
from datetime import datetime
from uuid import UUID

from sqlalchemy import Column, DateTime, ForeignKey, Index, String, func
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.sql.schema import Column as ColumnType

from common.db.database import Base


class OAuthAccountORM(Base):
    """OAuth account linking table for user-provider associations."""

    __tablename__ = "oauth_accounts"

    id: ColumnType[UUID] = Column(
        PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    user_id: ColumnType[UUID] = Column(
        PGUUID(as_uuid=True), ForeignKey("users.id"), nullable=False, index=True
    )
    provider: ColumnType[str] = Column(
        String(50), nullable=False
    )  # Dynamic provider support
    provider_account_id: ColumnType[str] = Column(String(255), nullable=False)
    created_at: ColumnType[datetime] = Column(DateTime, default=func.now())

    # Add composite unique constraint for provider + provider_account_id
    __table_args__ = (
        Index(
            "ix_oauth_accounts_provider_account",
            "provider",
            "provider_account_id",
            unique=True,
        ),
    )

    def __repr__(self) -> str:
        return f"<OAuthAccountORM(id={self.id}, provider='{self.provider}', user_id={self.user_id})>"
