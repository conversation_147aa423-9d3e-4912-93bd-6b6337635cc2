#!/usr/bin/env python3
"""
AI Development Workflow Main Entry Point

This is the main entry point for the AI development workflow system.
It delegates to the CLI module for command-line interface handling.
"""

import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent.parent.parent))

from tools.ai_dev_agents.utils.cli import main as cli_main


def main():
    """Main entry point - delegates to CLI module."""
    cli_main()


if __name__ == "__main__":
    main()
