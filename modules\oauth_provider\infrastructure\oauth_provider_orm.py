"""OAuth Provider ORM models for database persistence."""

import uuid
from datetime import datetime
from uuid import UUID

from sqlalchemy import Boolean, Column, DateTime, String, Text, func
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.sql.schema import Column as ColumnType

from common.db.database import Base


class OAuthProviderORM(Base):
    """OAuth provider configuration table."""

    __tablename__ = "oauth_providers"

    id: ColumnType[UUID] = Column(
        PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    name: ColumnType[str] = Column(String(50), unique=True, nullable=False, index=True)
    display_name: ColumnType[str] = Column(String(100), nullable=False)
    description: ColumnType[str] = Column(Text, nullable=True)
    is_enabled: ColumnType[bool] = Column(Boolean, default=True, nullable=False)
    icon_url: ColumnType[str] = Column(String(500), nullable=True)

    # OAuth configuration
    client_id: ColumnType[str] = Column(String(255), nullable=False)
    client_secret: ColumnType[str] = Column(
        String(255), nullable=False
    )  # Should be encrypted
    authorize_url: ColumnType[str] = Column(String(500), nullable=False)
    token_url: ColumnType[str] = Column(String(500), nullable=False)
    user_info_url: ColumnType[str] = Column(String(500), nullable=False)
    scope: ColumnType[str] = Column(String(255), nullable=False)

    # User data mapping configuration
    user_id_field: ColumnType[str] = Column(String(50), default="id", nullable=False)
    email_field: ColumnType[str] = Column(String(50), default="email", nullable=False)
    name_field: ColumnType[str] = Column(String(50), default="name", nullable=False)
    username_field: ColumnType[str] = Column(String(50), nullable=True)
    avatar_field: ColumnType[str] = Column(String(50), nullable=True)

    created_at: ColumnType[datetime] = Column(DateTime, default=func.now())
    updated_at: ColumnType[datetime] = Column(
        DateTime, default=func.now(), onupdate=func.now()
    )

    def __repr__(self) -> str:
        return f"<OAuthProviderORM(id={self.id}, name='{self.name}', enabled={self.is_enabled})>"
