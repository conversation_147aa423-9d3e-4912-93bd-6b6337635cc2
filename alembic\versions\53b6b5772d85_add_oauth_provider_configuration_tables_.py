"""Add OAuth provider configuration tables and update oauth_accounts

Revision ID: 53b6b5772d85
Revises: 6f9da6a0025a
Create Date: 2025-06-24 15:00:56.007138

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '53b6b5772d85'
down_revision = '6f9da6a0025a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.alter_column('provider',
               existing_type=postgresql.ENUM('GOOGLE', 'GITHUB', 'MICROSOFT', name='oauthprovider'),
               type_=sa.String(length=50),
               existing_nullable=False)
        batch_op.create_index('ix_oauth_accounts_provider_account', ['provider', 'provider_account_id'], unique=True)
        batch_op.create_index(batch_op.f('ix_oauth_accounts_user_id'), ['user_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_user_id'))
        batch_op.drop_index('ix_oauth_accounts_provider_account')
        batch_op.alter_column('provider',
               existing_type=sa.String(length=50),
               type_=postgresql.ENUM('GOOGLE', 'GITHUB', 'MICROSOFT', name='oauthprovider'),
               existing_nullable=False)

    # ### end Alembic commands ###
