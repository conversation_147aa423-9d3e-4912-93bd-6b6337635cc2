"""OAuth account repository interfaces."""

from abc import ABC, abstractmethod
from uuid import UUID

from .oauth_account_models import OAuthAccount


class OAuthAccountRepository(ABC):
    """Repository interface for OAuth account operations."""

    @abstractmethod
    def create(self, oauth_account: OAuthAccount) -> OAuthAccount:
        """Create a new OAuth account."""

    @abstractmethod
    def get_by_provider_and_id(
        self, provider: str, provider_account_id: str
    ) -> OAuthAccount | None:
        """Get OAuth account by provider and provider account ID."""

    @abstractmethod
    def get_by_user_id(self, user_id: UUID) -> list[OAuthAccount]:
        """Get all OAuth accounts for a user."""

    @abstractmethod
    def delete(self, oauth_account_id: UUID) -> bool:
        """Delete an OAuth account."""
