import os

import uvicorn
from dotenv import load_dotenv
from fastapi import FastAPI

# Load environment variables from .env file
load_dotenv()

# Import module routers
from modules.auth.interfaces import credential_auth_api, oauth_authentication_api
from modules.oauth_provider.interfaces import oauth_provider_api
from modules.user.interfaces import user_api

# 根据环境变量控制文档访问
DEBUG = os.getenv("AI4SE_MCP_HUB_DEBUG", "False").lower() in ("true", "1", "t")

app = FastAPI(
    title="AI4SE MCP Hub (Modular)",
    description="A hub for managing AI-for-SE models and contexts, now with a modular architecture.",
    version="0.2.0",
    # 生产环境禁用文档访问
    docs_url="/docs" if DEBUG else None,
    redoc_url="/redoc" if DEBUG else None,
)

# Include module routers
app.include_router(credential_auth_api.router, prefix="/api/v1")
app.include_router(oauth_authentication_api.router, prefix="/api/v1")
app.include_router(oauth_provider_api.router, prefix="/api/v1")
app.include_router(user_api.router, prefix="/api/v1")


@app.get("/", tags=["Root"])
async def read_root() -> dict[str, str]:
    return {"message": "Welcome to the AI4SE MCP Hub"}


if __name__ == "__main__":
    # Get server config from environment variables
    host = os.getenv("AI4SE_MCP_HUB_HOST", "127.0.0.1")
    port = int(os.getenv("AI4SE_MCP_HUB_PORT", "8000"))

    print(f"Starting server, listening on http://{host}:{port}")
    uvicorn.run("main:app", host=host, port=port, reload=True)
