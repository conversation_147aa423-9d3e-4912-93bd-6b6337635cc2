"""OAuth Provider management service."""

import uuid
from datetime import datetime
from uuid import UUID

from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig
from modules.oauth_provider.domain.oauth_provider_repositories import (
    OAuthProviderRepository,
)


class OAuthProviderService:
    """Service for managing OAuth provider configurations."""

    def __init__(self, oauth_provider_repo: OAuthProviderRepository):
        self.oauth_provider_repo = oauth_provider_repo

    def create_provider(
        self,
        name: str,
        display_name: str,
        client_id: str,
        client_secret: str,
        authorize_url: str,
        token_url: str,
        user_info_url: str,
        scope: str,
        description: str | None = None,
        is_enabled: bool = True,
        icon_url: str | None = None,
        user_id_field: str = "id",
        email_field: str = "email",
        name_field: str = "name",
        username_field: str | None = None,
        avatar_field: str | None = None,
    ) -> OAuthProviderConfig:
        """Create a new OAuth provider configuration."""
        # Check if provider name already exists
        existing_provider = self.oauth_provider_repo.get_by_name(name)
        if existing_provider:
            raise ValueError(f"OAuth provider with name '{name}' already exists")

        # Create new provider configuration
        now = datetime.now()
        provider_config = OAuthProviderConfig(
            id=uuid.uuid4(),
            name=name,
            display_name=display_name,
            description=description,
            is_enabled=is_enabled,
            icon_url=icon_url,
            client_id=client_id,
            client_secret=client_secret,
            authorize_url=authorize_url,
            token_url=token_url,
            user_info_url=user_info_url,
            scope=scope,
            user_id_field=user_id_field,
            email_field=email_field,
            name_field=name_field,
            username_field=username_field,
            avatar_field=avatar_field,
            created_at=now,
            updated_at=now,
        )

        return self.oauth_provider_repo.create(provider_config)

    def get_provider_by_name(self, name: str) -> OAuthProviderConfig | None:
        """Get OAuth provider configuration by name."""
        return self.oauth_provider_repo.get_by_name(name)

    def get_provider_by_id(self, provider_id: UUID) -> OAuthProviderConfig | None:
        """Get OAuth provider configuration by ID."""
        return self.oauth_provider_repo.get_by_id(provider_id)

    def get_all_providers(self) -> list[OAuthProviderConfig]:
        """Get all OAuth provider configurations."""
        return self.oauth_provider_repo.get_all()

    def get_enabled_providers(self) -> list[OAuthProviderConfig]:
        """Get all enabled OAuth provider configurations."""
        return self.oauth_provider_repo.get_all_enabled()

    def update_provider(
        self, provider_config: OAuthProviderConfig
    ) -> OAuthProviderConfig:
        """Update an OAuth provider configuration."""
        # Update timestamp
        provider_config.updated_at = datetime.now()
        return self.oauth_provider_repo.update(provider_config)

    def delete_provider(self, provider_id: UUID) -> bool:
        """Delete an OAuth provider configuration."""
        return self.oauth_provider_repo.delete(provider_id)

    def enable_provider(self, provider_id: UUID) -> bool:
        """Enable an OAuth provider."""
        provider = self.oauth_provider_repo.get_by_id(provider_id)
        if not provider:
            return False

        provider.is_enabled = True
        provider.updated_at = datetime.now()
        self.oauth_provider_repo.update(provider)
        return True

    def disable_provider(self, provider_id: UUID) -> bool:
        """Disable an OAuth provider."""
        provider = self.oauth_provider_repo.get_by_id(provider_id)
        if not provider:
            return False

        provider.is_enabled = False
        provider.updated_at = datetime.now()
        self.oauth_provider_repo.update(provider)
        return True

    def validate_provider_config(self, provider_config: OAuthProviderConfig) -> dict:
        """Validate OAuth provider configuration."""
        validation_results = []
        is_valid = True

        # Validate required fields
        required_fields = [
            ("name", provider_config.name),
            ("display_name", provider_config.display_name),
            ("client_id", provider_config.client_id),
            ("client_secret", provider_config.client_secret),
            ("authorize_url", str(provider_config.authorize_url)),
            ("token_url", str(provider_config.token_url)),
            ("user_info_url", str(provider_config.user_info_url)),
            ("scope", provider_config.scope),
        ]

        for field_name, field_value in required_fields:
            if not field_value or (
                isinstance(field_value, str) and field_value.strip() == ""
            ):
                validation_results.append(
                    {
                        "field": field_name,
                        "status": "error",
                        "message": f"Field '{field_name}' is required but empty",
                    }
                )
                is_valid = False
            else:
                validation_results.append(
                    {
                        "field": field_name,
                        "status": "valid",
                        "message": f"Field '{field_name}' is valid",
                    }
                )

        # Validate URLs
        url_fields = [
            ("authorize_url", str(provider_config.authorize_url)),
            ("token_url", str(provider_config.token_url)),
            ("user_info_url", str(provider_config.user_info_url)),
        ]

        for field_name, url_value in url_fields:
            if url_value and not url_value.startswith(("http://", "https://")):
                validation_results.append(
                    {
                        "field": field_name,
                        "status": "warning",
                        "message": f"URL '{field_name}' should start with http:// or https://",
                    }
                )

        # Validate user data mapping fields
        mapping_fields = [
            ("user_id_field", provider_config.user_id_field),
            ("email_field", provider_config.email_field),
            ("name_field", provider_config.name_field),
        ]

        for field_name, field_value in mapping_fields:
            if not field_value or field_value.strip() == "":
                validation_results.append(
                    {
                        "field": field_name,
                        "status": "error",
                        "message": f"Mapping field '{field_name}' is required but empty",
                    }
                )
                is_valid = False
            else:
                validation_results.append(
                    {
                        "field": field_name,
                        "status": "valid",
                        "message": f"Mapping field '{field_name}' is valid",
                    }
                )

        return {
            "provider_name": provider_config.name,
            "is_valid": is_valid,
            "validation_results": validation_results,
            "summary": {
                "total_checks": len(validation_results),
                "valid_checks": len(
                    [r for r in validation_results if r["status"] == "valid"]
                ),
                "error_checks": len(
                    [r for r in validation_results if r["status"] == "error"]
                ),
                "warning_checks": len(
                    [r for r in validation_results if r["status"] == "warning"]
                ),
            },
        }
