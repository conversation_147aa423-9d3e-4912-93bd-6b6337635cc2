"""create oauth_accounts table

Revision ID: 61b8ee2fe814
Revises: f3ab7c588612
Create Date: 2025-06-23 18:52:24.333213

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '61b8ee2fe814'
down_revision = 'f3ab7c588612'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('oauth_accounts',
    sa.<PERSON>umn('id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('user_id', sa.Integer(), nullable=False),
    sa.<PERSON>umn('provider', sa.Enum('GOOGLE', 'GITHUB', 'MICROSOFT', name='oauthprovider'), nullable=False),
    sa.Column('provider_account_id', sa.String(length=255), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_oauth_accounts_id'), ['id'], unique=False)

    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_id'), ['id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_id'))

    with op.batch_alter_table('oauth_accounts', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_oauth_accounts_id'))

    op.drop_table('oauth_accounts')
    # ### end Alembic commands ###
