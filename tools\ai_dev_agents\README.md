# AI Development Agents

一个智能的 AI 驱动开发工作流系统，将产品需求文档（PRD）转换为结构化的开发需求和 AI 代码生成提示词。

## 核心功能

- **业务分析**: 智能分析 PRD 文档，提取业务实体、需求和用户故事
- **领域建模**: 自动生成领域驱动设计（DDD）模型和架构
- **需求生成**: 创建详细的技术需求和实现规范
- **提示构建**: 生成优化的 AI 代码生成提示词
- **工作流编排**: 端到端自动化开发规划流程

## 架构设计

```text
PRD文档 → 业务分析Agent → 领域建模Agent → 需求生成Agent → 提示词构建Agent → AI开发提示词
```

### Agent组件

1. **BusinessAnalyzerAgent** - 业务需求分析
   - 提取核心业务概念和实体
   - 识别用户角色和使用场景
   - 分析业务流程和规则
   - 质量评估和多轮优化

2. **DomainModelerAgent** - DDD领域建模
   - 识别边界上下文(Bounded Context)
   - 设计聚合根和实体
   - 定义值对象和领域服务

3. **RequirementsGeneratorAgent** - 技术需求生成
   - 生成用户故事和验收标准
   - 设计API接口规范
   - 规划数据库模式和测试策略

4. **PromptBuilderAgent** - AI提示词构建
   - 整合项目上下文和约束
   - 构建结构化的开发指令
   - 包含代码示例和参考模式

## 项目结构

```text
ai_dev_agents/
├── core/                      # 核心框架组件
│   ├── base_agent.py         # 基础代理类和通用接口
│   └── orchestrator.py       # 工作流编排引擎
├── agents/                    # 智能代理
│   ├── business_analyzer.py  # 业务需求分析
│   ├── domain_modeler.py     # DDD 领域建模
│   ├── requirements_generator.py  # 技术需求生成
│   └── prompt_builder.py     # AI 提示词构建
├── utils/                     # 工具组件
│   ├── config_manager.py     # 配置管理
│   └── cli.py               # 命令行接口
├── prompts/                   # 外部提示词模板
│   ├── business_analysis.yaml
│   ├── domain_modeling.yaml
│   ├── requirements_generation.yaml
│   └── prompt_building.yaml
├── tests/                     # 测试套件
├── examples/                  # 使用示例
├── config.yaml              # 配置文件
├── main.py                   # 主入口文件
└── README.md                 # 本文档
```

## 快速开始

### 1. 安装依赖

```bash
# 使用 uv（推荐）
uv add langchain-openai pydantic pyyaml

# 或使用 pip
pip install langchain-openai pydantic pyyaml
```

### 2. 配置 LLM

编辑 `config.yaml` 文件，配置您的 LLM 提供商：

```yaml
llm:
  provider: "openrouter"  # 或 "openai", "anthropic"
  openrouter:
    api_key: "your-api-key-here"
    model: "deepseek/deepseek-r1-0528:free"
    temperature: 0.1
    max_tokens: 4000
```

### 3. 运行工作流

```bash
# 完整工作流
python main.py workflow ./docs/requirements.md

# 分步执行
python main.py business-analysis ./docs/requirements.md --output business_analysis.json
python main.py domain-modeling business_analysis.json --output domain_model.json
python main.py generate-requirements domain_model.json --module user_management --output requirements.json
python main.py build-prompt requirements.json --output ai_prompt.txt
```

## 使用方法

### 命令行接口

```bash
# 查看帮助
python main.py --help

# 运行完整工作流
python main.py workflow ./docs/prd.md

# 指定输出目录
python main.py workflow ./docs/prd.md --output ./output

# 处理特定模块
python main.py workflow ./docs/prd.md --modules user_management,order_management

# 使用模型预设和详细输出
python main.py --preset high_quality --verbose workflow ./docs/prd.md
```

### Python API

```python
from ai_dev_agents.core.orchestrator import AIDevWorkflowOrchestrator
from ai_dev_agents.core.base_agent import WorkflowContext

# 初始化编排器
orchestrator = AIDevWorkflowOrchestrator(verbose=True)

# 创建工作流上下文
context = orchestrator.create_context(
    project_root="./my_project",
    project_rules="项目特定规则..."
)

# 运行完整工作流
result = orchestrator.run_complete_workflow(
    prd_content="您的 PRD 内容...",
    context=context
)

if result.success:
    print("工作流执行成功！")
    print(f"生成的提示词: {result.data}")
else:
    print(f"执行失败: {result.error}")
```

## 命令行参数详解

### 全局参数

- `--verbose, -v`: 启用详细输出
- `--project-root PROJECT_ROOT`: 项目根目录（默认：当前目录）
- `--config CONFIG, -c CONFIG`: 配置文件路径
- `--preset PRESET, -p PRESET`: 模型预设（如 high_quality, economical）

### workflow 命令

```bash
python main.py workflow <prd_file> [options]
```

**参数:**
- `prd_file`: PRD文档路径（必需）
- `--output, -o`: 输出目录（可选）
- `--modules, -m`: 要处理的模块列表，逗号分隔（可选）

### business-analysis 命令

```bash
python main.py business-analysis <prd_file> [options]
```

**参数:**
- `prd_file`: PRD文档路径（必需）
- `--output, -o`: 输出文件路径（可选）

### domain-modeling 命令

```bash
python main.py domain-modeling <business_analysis> [options]
```

**参数:**
- `business_analysis`: 业务分析JSON文件路径（必需）
- `--output, -o`: 输出文件路径（可选）

### generate-requirements 命令

```bash
python main.py generate-requirements <domain_model> [options]
```

**参数:**
- `domain_model`: 领域模型JSON文件路径（必需）
- `--module`: 要生成需求的特定模块（可选）
- `--output, -o`: 输出文件路径（可选）

### build-prompt 命令

```bash
python main.py build-prompt <requirements> [options]
```

**参数:**
- `requirements`: 需求JSON文件路径（必需）
- `--output, -o`: 输出文件路径（可选）

## 配置说明

### LLM 配置

支持多种 LLM 提供商：

- **OpenRouter**: 推荐，支持多种模型，包括免费的 DeepSeek R1
- **OpenAI**: 官方 GPT 模型
- **Anthropic**: Claude 系列模型

### 模型预设

- `default`: DeepSeek R1 免费版，推理能力强
- `high_quality`: 更低温度，更高质量
- `creative`: 更高温度，更有创意

### 系统参数

- `log_level`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `verbose`: 是否启用详细输出

## 测试

```bash
# 运行基础测试
python test_reorganization.py

# 运行真实 LLM 测试（需要配置 API key）
python test_real_llm.py
```

## 示例

查看 `examples/` 目录中的示例代码，了解如何：

- 使用 Python API 进行开发
- 自定义 Agent 行为
- 集成到现有项目中

## 开发指南

### 添加新的 Agent

1. 在 `agents/` 目录创建新的 Agent 类
2. 继承 `BaseAgent` 类
3. 实现 `process` 方法
4. 在 `prompts/` 目录添加对应的提示词模板
5. 更新 `orchestrator.py` 中的工作流

### 自定义提示词

编辑 `prompts/` 目录中的 YAML 文件，自定义各个 Agent 的提示词模板。

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！

## 支持

如有问题，请在 GitHub 上创建 Issue。
