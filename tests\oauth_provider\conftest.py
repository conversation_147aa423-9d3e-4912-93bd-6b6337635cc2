"""OAuth provider test configuration."""

import uuid
from datetime import datetime

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

from common.db.database import Base
from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig


@pytest.fixture
def db_session():
    """Create a database session for testing."""
    # Use in-memory SQLite for testing
    engine = create_engine("sqlite:///:memory:", echo=False)

    # Create all tables
    Base.metadata.create_all(engine)

    # Create session
    session_maker = sessionmaker(bind=engine)

    with session_maker() as session:
        yield session


@pytest.fixture
def github_provider_config() -> OAuthProviderConfig:
    """Create a GitHub OAuth provider configuration for testing."""
    return OAuthProviderConfig(
        id=uuid.uuid4(),
        name="github",
        display_name="GitHub",
        description="GitHub OAuth provider",
        is_enabled=True,
        icon_url="https://github.com/favicon.ico",
        client_id="test_github_client_id",
        client_secret="test_github_client_secret",
        authorize_url="https://github.com/login/oauth/authorize",
        token_url="https://github.com/login/oauth/access_token",
        user_info_url="https://api.github.com/user",
        scope="user:email",
        user_id_field="id",
        email_field="email",
        name_field="name",
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )


@pytest.fixture
def google_provider_config() -> OAuthProviderConfig:
    """Create a Google OAuth provider configuration for testing."""
    return OAuthProviderConfig(
        id=uuid.uuid4(),
        name="google",
        display_name="Google",
        description="Google OAuth provider",
        is_enabled=True,
        icon_url="https://google.com/favicon.ico",
        client_id="test_google_client_id",
        client_secret="test_google_client_secret",
        authorize_url="https://accounts.google.com/o/oauth2/v2/auth",
        token_url="https://oauth2.googleapis.com/token",
        user_info_url="https://www.googleapis.com/oauth2/v2/userinfo",
        scope="openid email profile",
        user_id_field="id",
        email_field="email",
        name_field="name",
        created_at=datetime.now(),
        updated_at=datetime.now(),
    )
