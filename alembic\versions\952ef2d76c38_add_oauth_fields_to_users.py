"""Add OAuth fields to users

Revision ID: 952ef2d76c38
Revises: 33dd513acd87
Create Date: 2025-06-23 09:13:47.734357

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '952ef2d76c38'
down_revision = '33dd513acd87'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.add_column(sa.Column('oauth_provider', sa.String(), nullable=True))
        batch_op.add_column(sa.Column('oauth_id', sa.String(), nullable=True))
        batch_op.alter_column('username',
               existing_type=sa.VARCHAR(),
               nullable=False)
        batch_op.alter_column('email',
               existing_type=sa.VARCHAR(),
               nullable=False)
        batch_op.drop_constraint(batch_op.f('users_employee_id_key'), type_='unique')
        batch_op.drop_constraint(batch_op.f('users_it_code_key'), type_='unique')
        batch_op.drop_constraint(batch_op.f('users_work_email_key'), type_='unique')

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_unique_constraint(batch_op.f('users_work_email_key'), ['work_email'], postgresql_nulls_not_distinct=False)
        batch_op.create_unique_constraint(batch_op.f('users_it_code_key'), ['it_code'], postgresql_nulls_not_distinct=False)
        batch_op.create_unique_constraint(batch_op.f('users_employee_id_key'), ['employee_id'], postgresql_nulls_not_distinct=False)
        batch_op.alter_column('email',
               existing_type=sa.VARCHAR(),
               nullable=True)
        batch_op.alter_column('username',
               existing_type=sa.VARCHAR(),
               nullable=True)
        batch_op.drop_column('oauth_id')
        batch_op.drop_column('oauth_provider')

    # ### end Alembic commands ###
