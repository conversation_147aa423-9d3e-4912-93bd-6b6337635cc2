"""Shared OAuth client service for cross-module OAuth functionality."""

import asyncio
from typing import Any
from urllib.parse import urlencode

import httpx
from authlib.integrations.httpx_client import AsyncOAuth2Client
from authlib.oauth2.rfc6749.errors import OAuth2Error

from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig
from modules.oauth_provider.domain.oauth_provider_repositories import (
    OAuthProviderRepository,
)


class OAuthClientService:
    """Shared service for handling OAuth client interactions with dynamically configured providers."""

    def __init__(
        self,
        oauth_provider_repo: OAuthProviderRepository,
        oauth_base_url: str,
    ):
        self.oauth_provider_repo = oauth_provider_repo
        self.oauth_base_url = oauth_base_url
        self._config_cache: dict[str, OAuthProviderConfig] = {}
        self._cache_lock = asyncio.Lock()

    async def get_provider_config(
        self, provider_name: str
    ) -> OAuthProviderConfig | None:
        """Get provider configuration from cache or database."""
        async with self._cache_lock:
            # Check cache first
            if provider_name in self._config_cache:
                config = self._config_cache[provider_name]
                if config.is_enabled:
                    return config
                else:
                    # Remove disabled provider from cache
                    del self._config_cache[provider_name]
                    return None

            # Load from database
            config = self.oauth_provider_repo.get_by_name(provider_name)
            if config and config.is_enabled:
                self._config_cache[provider_name] = config
                return config

            return None

    async def get_all_enabled_providers(self) -> list[OAuthProviderConfig]:
        """Get all enabled OAuth provider configurations."""
        return self.oauth_provider_repo.get_all_enabled()

    def _get_redirect_uri(self, provider_name: str) -> str:
        """Generate redirect URI for the specified provider."""
        return f"{self.oauth_base_url}/api/v1/auth/oauth/{provider_name}/callback"

    async def generate_authorization_url(
        self, provider_name: str, state: str | None = None
    ) -> str:
        """Generate OAuth authorization URL for the specified provider."""
        config = await self.get_provider_config(provider_name)
        if not config:
            raise ValueError(f"OAuth provider '{provider_name}' not found or disabled")

        redirect_uri = self._get_redirect_uri(provider_name)

        params = {
            "client_id": config.client_id,
            "redirect_uri": redirect_uri,
            "scope": config.scope,
            "response_type": "code",
        }

        if state:
            params["state"] = state

        return f"{config.authorize_url}?{urlencode(params)}"

    async def exchange_code_for_token(
        self, provider_name: str, code: str
    ) -> dict[str, Any]:
        """Exchange authorization code for access token."""
        config = await self.get_provider_config(provider_name)
        if not config:
            raise ValueError(f"OAuth provider '{provider_name}' not found or disabled")

        redirect_uri = self._get_redirect_uri(provider_name)

        oauth_client = AsyncOAuth2Client(
            client_id=config.client_id,
            client_secret=config.client_secret,
        )

        try:
            token = await oauth_client.fetch_token(
                str(config.token_url),
                code=code,
                redirect_uri=redirect_uri,
            )
            return token
        except OAuth2Error as e:
            raise ValueError(f"Failed to exchange code for token: {str(e)}") from e

    async def get_user_info(
        self, provider_name: str, access_token: str
    ) -> dict[str, Any]:
        """Fetch user information from OAuth provider using access token."""
        config = await self.get_provider_config(provider_name)
        if not config:
            raise ValueError(f"OAuth provider '{provider_name}' not found or disabled")

        async with httpx.AsyncClient() as client:
            headers = {"Authorization": f"Bearer {access_token}"}

            try:
                response = await client.get(str(config.user_info_url), headers=headers)
                response.raise_for_status()
                user_data = response.json()

                # Normalize user data using provider configuration
                return self._normalize_user_data(config, user_data)
            except httpx.HTTPError as e:
                raise ValueError(f"Failed to fetch user info: {str(e)}") from e

    def _normalize_user_data(
        self, config: OAuthProviderConfig, raw_data: dict[str, Any]
    ) -> dict[str, Any]:
        """Normalize user data using provider configuration mapping."""
        mapping = config.get_user_data_mapping()

        # Extract user ID (required)
        oauth_id = raw_data.get(mapping["user_id_field"])
        if not oauth_id:
            raise ValueError(
                f"User ID field '{mapping['user_id_field']}' not found in provider response"
            )

        # Extract email (handle various None representations)
        email = raw_data.get(mapping["email_field"])
        if (
            not email
            or email == "None"
            or email == "null"
            or str(email).lower() == "none"
            or str(email).strip() == ""
        ):
            email = None

        # Extract name
        name = raw_data.get(mapping["name_field"])

        # Extract username (optional)
        username = None
        if "username_field" in mapping and mapping["username_field"]:
            username = raw_data.get(mapping["username_field"])

        # If no username field configured, generate from email or use provider pattern
        if not username and email:
            email_prefix = email.split("@")[0]
            if len(email_prefix) >= 3:
                username = email_prefix
            else:
                username = f"{config.name}_{oauth_id}"
        elif not username:
            username = f"{config.name}_{oauth_id}"

        # Extract avatar URL (optional)
        avatar_url = None
        if "avatar_field" in mapping and mapping["avatar_field"]:
            avatar_url = raw_data.get(mapping["avatar_field"])

        return {
            "oauth_id": str(oauth_id),
            "email": email,
            "name": name,
            "username": username,
            "avatar_url": avatar_url,
        }

    async def handle_oauth_flow(self, provider_name: str, code: str) -> dict[str, Any]:
        """Complete OAuth flow: exchange code for token and fetch user info."""
        # Exchange code for access token
        token_data = await self.exchange_code_for_token(provider_name, code)
        access_token = token_data["access_token"]

        # Fetch user information
        user_info = await self.get_user_info(provider_name, access_token)

        return {
            "user_info": user_info,
            "token_data": token_data,
        }

    async def clear_cache(self) -> None:
        """Clear the provider configuration cache."""
        async with self._cache_lock:
            self._config_cache.clear()

    async def refresh_provider_cache(self, provider_name: str) -> None:
        """Refresh cache for a specific provider."""
        async with self._cache_lock:
            if provider_name in self._config_cache:
                del self._config_cache[provider_name]

    async def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        async with self._cache_lock:
            return {
                "cached_providers": list(self._config_cache.keys()),
                "cache_size": len(self._config_cache),
                "enabled_providers": [
                    name
                    for name, config in self._config_cache.items()
                    if config.is_enabled
                ],
            }

    async def warm_cache(self) -> None:
        """Pre-load all enabled providers into cache."""
        providers = self.oauth_provider_repo.get_all_enabled()
        async with self._cache_lock:
            for provider in providers:
                self._config_cache[provider.name] = provider
