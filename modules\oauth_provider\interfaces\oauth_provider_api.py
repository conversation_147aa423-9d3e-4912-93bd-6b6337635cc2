"""OAuth Provider management API endpoints."""

import uuid
from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from common.db.database import get_db
from modules.oauth_provider.domain.oauth_provider_models import OAuthProviderConfig
from modules.oauth_provider.domain.oauth_provider_repositories import (
    OAuthProviderRepository,
)
from modules.oauth_provider.infrastructure.oauth_provider_repositories import (
    OAuthProviderRepositoryImpl,
)
from modules.oauth_provider.interfaces.oauth_provider_schemas import (
    OAuthProviderCreateRequest,
    OAuthProviderListResponse,
    OAuthProviderPublicInfo,
    OAuthProviderResponse,
    OAuthProviderUpdateRequest,
)

router = APIRouter(prefix="/oauth/providers", tags=["OAuth Provider Management"])


def get_oauth_provider_repository(
    session: Session = Depends(get_db),
) -> OAuthProviderRepository:
    """Dependency to get OAuth provider repository."""
    return OAuthProviderRepositoryImpl(session)


@router.get(
    "/public",
    response_model=list[OAuthProviderPublicInfo],
    summary="Get public OAuth provider information",
    description="Get list of enabled OAuth providers for client-side display",
    response_description="List of enabled OAuth providers with public information",
)
def get_public_oauth_providers(
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> list[OAuthProviderPublicInfo]:
    """Get public information about enabled OAuth providers."""
    providers = oauth_provider_repo.get_all_enabled()

    return [
        OAuthProviderPublicInfo(
            name=provider.name,
            display_name=provider.display_name,
            description=provider.description,
            icon_url=str(provider.icon_url) if provider.icon_url else None,
        )
        for provider in providers
    ]


@router.get(
    "",
    response_model=OAuthProviderListResponse,
    summary="Get all OAuth providers",
    description="Get list of all OAuth provider configurations (admin only)",
    response_description="List of OAuth provider configurations",
)
def get_oauth_providers(
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> OAuthProviderListResponse:
    """Get all OAuth provider configurations."""
    providers = oauth_provider_repo.get_all()

    provider_responses = [
        OAuthProviderResponse(
            id=provider.id,
            name=provider.name,
            display_name=provider.display_name,
            description=provider.description,
            is_enabled=provider.is_enabled,
            icon_url=str(provider.icon_url) if provider.icon_url else None,
            client_id=provider.client_id,
            authorize_url=str(provider.authorize_url),
            token_url=str(provider.token_url),
            user_info_url=str(provider.user_info_url),
            scope=provider.scope,
            user_id_field=provider.user_id_field,
            email_field=provider.email_field,
            name_field=provider.name_field,
            username_field=provider.username_field,
            avatar_field=provider.avatar_field,
            created_at=provider.created_at.isoformat(),
            updated_at=provider.updated_at.isoformat(),
        )
        for provider in providers
    ]

    return OAuthProviderListResponse(
        providers=provider_responses,
        total=len(provider_responses),
    )


@router.get(
    "/{provider_name}",
    response_model=OAuthProviderResponse,
    summary="Get OAuth provider by name",
    description="Get specific OAuth provider configuration by name",
    response_description="OAuth provider configuration",
    responses={
        404: {"description": "OAuth provider not found"},
    },
)
def get_oauth_provider(
    provider_name: str,
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> OAuthProviderResponse:
    """Get OAuth provider configuration by name."""
    provider = oauth_provider_repo.get_by_name(provider_name)

    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"OAuth provider '{provider_name}' not found",
        )

    return OAuthProviderResponse(
        id=provider.id,
        name=provider.name,
        display_name=provider.display_name,
        description=provider.description,
        is_enabled=provider.is_enabled,
        icon_url=str(provider.icon_url) if provider.icon_url else None,
        client_id=provider.client_id,
        authorize_url=str(provider.authorize_url),
        token_url=str(provider.token_url),
        user_info_url=str(provider.user_info_url),
        scope=provider.scope,
        user_id_field=provider.user_id_field,
        email_field=provider.email_field,
        name_field=provider.name_field,
        username_field=provider.username_field,
        avatar_field=provider.avatar_field,
        created_at=provider.created_at.isoformat(),
        updated_at=provider.updated_at.isoformat(),
    )


@router.post(
    "",
    response_model=OAuthProviderResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create OAuth provider",
    description="Create a new OAuth provider configuration",
    response_description="Created OAuth provider configuration",
    responses={
        400: {"description": "Invalid request data or provider name already exists"},
        201: {"description": "OAuth provider created successfully"},
    },
)
def create_oauth_provider(
    request: OAuthProviderCreateRequest,
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> OAuthProviderResponse:
    """Create a new OAuth provider configuration."""
    # Check if provider name already exists
    existing_provider = oauth_provider_repo.get_by_name(request.name)
    if existing_provider:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth provider with name '{request.name}' already exists",
        )

    # Create new provider configuration
    now = datetime.now()
    provider_config = OAuthProviderConfig(
        id=uuid.uuid4(),
        name=request.name,
        display_name=request.display_name,
        description=request.description,
        is_enabled=request.is_enabled,
        icon_url=request.icon_url,
        client_id=request.client_id,
        client_secret=request.client_secret,
        authorize_url=request.authorize_url,
        token_url=request.token_url,
        user_info_url=request.user_info_url,
        scope=request.scope,
        user_id_field=request.user_id_field,
        email_field=request.email_field,
        name_field=request.name_field,
        username_field=request.username_field,
        avatar_field=request.avatar_field,
        created_at=now,
        updated_at=now,
    )

    created_provider = oauth_provider_repo.create(provider_config)

    return OAuthProviderResponse(
        id=created_provider.id,
        name=created_provider.name,
        display_name=created_provider.display_name,
        description=created_provider.description,
        is_enabled=created_provider.is_enabled,
        icon_url=str(created_provider.icon_url) if created_provider.icon_url else None,
        client_id=created_provider.client_id,
        authorize_url=str(created_provider.authorize_url),
        token_url=str(created_provider.token_url),
        user_info_url=str(created_provider.user_info_url),
        scope=created_provider.scope,
        user_id_field=created_provider.user_id_field,
        email_field=created_provider.email_field,
        name_field=created_provider.name_field,
        username_field=created_provider.username_field,
        avatar_field=created_provider.avatar_field,
        created_at=created_provider.created_at.isoformat(),
        updated_at=created_provider.updated_at.isoformat(),
    )


@router.put(
    "/{provider_name}",
    response_model=OAuthProviderResponse,
    summary="Update OAuth provider",
    description="Update an existing OAuth provider configuration",
    response_description="Updated OAuth provider configuration",
    responses={
        404: {"description": "OAuth provider not found"},
        400: {"description": "Invalid request data"},
    },
)
def update_oauth_provider(
    provider_name: str,
    request: OAuthProviderUpdateRequest,
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> OAuthProviderResponse:
    """Update an existing OAuth provider configuration."""
    # Get existing provider
    existing_provider = oauth_provider_repo.get_by_name(provider_name)
    if not existing_provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"OAuth provider '{provider_name}' not found",
        )

    # Update fields that are provided
    update_data = request.model_dump(exclude_unset=True)

    for field, value in update_data.items():
        setattr(existing_provider, field, value)

    # Update timestamp
    existing_provider.updated_at = datetime.now()

    updated_provider = oauth_provider_repo.update(existing_provider)

    return OAuthProviderResponse(
        id=updated_provider.id,
        name=updated_provider.name,
        display_name=updated_provider.display_name,
        description=updated_provider.description,
        is_enabled=updated_provider.is_enabled,
        icon_url=str(updated_provider.icon_url) if updated_provider.icon_url else None,
        client_id=updated_provider.client_id,
        authorize_url=str(updated_provider.authorize_url),
        token_url=str(updated_provider.token_url),
        user_info_url=str(updated_provider.user_info_url),
        scope=updated_provider.scope,
        user_id_field=updated_provider.user_id_field,
        email_field=updated_provider.email_field,
        name_field=updated_provider.name_field,
        username_field=updated_provider.username_field,
        avatar_field=updated_provider.avatar_field,
        created_at=updated_provider.created_at.isoformat(),
        updated_at=updated_provider.updated_at.isoformat(),
    )


@router.delete(
    "/{provider_name}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete OAuth provider",
    description="Delete an OAuth provider configuration",
    responses={
        404: {"description": "OAuth provider not found"},
        204: {"description": "OAuth provider deleted successfully"},
    },
)
def delete_oauth_provider(
    provider_name: str,
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> None:
    """Delete an OAuth provider configuration."""
    # Get existing provider
    existing_provider = oauth_provider_repo.get_by_name(provider_name)
    if not existing_provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"OAuth provider '{provider_name}' not found",
        )

    # Delete the provider
    success = oauth_provider_repo.delete(existing_provider.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete OAuth provider",
        )


# Cache management and testing endpoints have been removed
# These functionalities are now handled by the shared OAuth client service


@router.post(
    "/{provider_name}/validate",
    summary="Validate OAuth provider configuration",
    description="Validate OAuth provider configuration fields and URLs",
    response_description="Validation result",
    responses={
        200: {"description": "Provider configuration validation result"},
        404: {"description": "OAuth provider not found"},
    },
)
def validate_oauth_provider_configuration(
    provider_name: str,
    oauth_provider_repo: OAuthProviderRepository = Depends(
        get_oauth_provider_repository
    ),
) -> dict:
    """Validate OAuth provider configuration."""
    # Check if provider exists
    provider = oauth_provider_repo.get_by_name(provider_name)
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"OAuth provider '{provider_name}' not found",
        )

    validation_results = []
    is_valid = True

    # Validate required fields
    required_fields = [
        ("name", provider.name),
        ("display_name", provider.display_name),
        ("client_id", provider.client_id),
        ("client_secret", provider.client_secret),
        ("authorize_url", str(provider.authorize_url)),
        ("token_url", str(provider.token_url)),
        ("user_info_url", str(provider.user_info_url)),
        ("scope", provider.scope),
    ]

    for field_name, field_value in required_fields:
        if not field_value or (
            isinstance(field_value, str) and field_value.strip() == ""
        ):
            validation_results.append(
                {
                    "field": field_name,
                    "status": "error",
                    "message": f"Field '{field_name}' is required but empty",
                }
            )
            is_valid = False
        else:
            validation_results.append(
                {
                    "field": field_name,
                    "status": "valid",
                    "message": f"Field '{field_name}' is valid",
                }
            )

    # Validate URLs
    url_fields = [
        ("authorize_url", str(provider.authorize_url)),
        ("token_url", str(provider.token_url)),
        ("user_info_url", str(provider.user_info_url)),
    ]

    for field_name, url_value in url_fields:
        if url_value and not url_value.startswith(("http://", "https://")):
            validation_results.append(
                {
                    "field": field_name,
                    "status": "warning",
                    "message": f"URL '{field_name}' should start with http:// or https://",
                }
            )

    # Validate user data mapping fields
    mapping_fields = [
        ("user_id_field", provider.user_id_field),
        ("email_field", provider.email_field),
        ("name_field", provider.name_field),
    ]

    for field_name, field_value in mapping_fields:
        if not field_value or field_value.strip() == "":
            validation_results.append(
                {
                    "field": field_name,
                    "status": "error",
                    "message": f"Mapping field '{field_name}' is required but empty",
                }
            )
            is_valid = False
        else:
            validation_results.append(
                {
                    "field": field_name,
                    "status": "valid",
                    "message": f"Mapping field '{field_name}' is valid",
                }
            )

    return {
        "provider_name": provider_name,
        "is_valid": is_valid,
        "validation_results": validation_results,
        "summary": {
            "total_checks": len(validation_results),
            "valid_checks": len(
                [r for r in validation_results if r["status"] == "valid"]
            ),
            "error_checks": len(
                [r for r in validation_results if r["status"] == "error"]
            ),
            "warning_checks": len(
                [r for r in validation_results if r["status"] == "warning"]
            ),
        },
    }
