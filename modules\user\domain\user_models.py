from datetime import datetime
from enum import Enum
from typing import Any
from uuid import UUID

from pydantic import BaseModel, ConfigDict, EmailStr, Field, field_validator


class UserStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"


class User(BaseModel):
    """
    Domain Entity: User
    This is the core domain model, containing only user state and identity information,
    without any infrastructure logic (such as cryptography).
    """

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    username: str = Field(..., min_length=3, max_length=50)
    email: EmailStr | None = None
    hashed_password: str | None = None
    status: UserStatus = Field(default=UserStatus.ACTIVE, description="User status")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    @field_validator("hashed_password")
    @classmethod
    def validate_hashed_password(cls, v):
        """Validate that hashed_password is not empty when provided."""
        if v is not None and not v.strip():
            raise ValueError("Hashed password cannot be empty")
        return v

    @field_validator("email", mode="before")
    @classmethod
    def validate_email(cls, v: Any) -> str | None:
        """Validate email field, converting various 'None' representations to actual None"""
        if v is None:
            return None

        # Convert to string for checking
        str_v = str(v).strip()

        # Check for various representations of None/null
        if (
            str_v == ""
            or str_v.lower() == "none"
            or str_v.lower() == "null"
            or str_v == "None"
            or str_v == "NULL"
        ):
            return None

        return str_v

    def __eq__(self, other):
        """Compare users based on their ID."""
        if not isinstance(other, User):
            return False
        return self.id == other.id

    def __hash__(self):
        """Hash based on user ID."""
        return hash(self.id)
