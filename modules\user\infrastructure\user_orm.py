"""User ORM models."""

import uuid
from datetime import datetime
from uuid import UUID

from sqlalchemy import Column, DateTime, Enum, String, func
from sqlalchemy.dialects.postgresql import UUID as PGUUID
from sqlalchemy.sql.schema import Column as ColumnType

from common.db.database import Base
from modules.user.domain.user_models import UserStatus


class UserORM(Base):
    """User ORM model."""

    __tablename__ = "users"

    id: ColumnType[UUID] = Column(
        PGUUID(as_uuid=True), primary_key=True, default=uuid.uuid4
    )
    username: ColumnType[str] = Column(
        String(50), unique=True, nullable=False, index=True
    )
    email: ColumnType[str] = Column(String(100), unique=True, nullable=True, index=True)
    hashed_password: ColumnType[str] = Column(String(255), nullable=True)
    status: ColumnType[UserStatus] = Column(
        Enum(UserStatus), default=UserStatus.ACTIVE, nullable=False
    )
    created_at: ColumnType[datetime] = Column(DateTime, default=func.now())
    updated_at: ColumnType[datetime] = Column(
        DateTime, default=func.now(), onupdate=func.now()
    )

    def __repr__(self) -> str:
        return (
            f"<UserORM(id={self.id}, username='{self.username}', status={self.status})>"
        )


# Alias for backward compatibility
UserDB = UserORM
