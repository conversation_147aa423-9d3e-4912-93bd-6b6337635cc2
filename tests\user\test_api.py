import uuid

from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from modules.user.domain.user_models import UserStatus
from modules.user.infrastructure.user_orm import UserORM


def should_return_list_of_users_when_valid_request(
    test_client: TestClient, test_user: UserORM, db: Session
) -> None:
    # Verify the test user exists in the database
    db_user = db.query(UserORM).filter(UserORM.id == test_user.id).first()
    assert db_user is not None, f"Test user {test_user.id} not found in database"

    # Test with a larger limit to ensure we can find our test user
    response = test_client.get("/api/v1/users/?limit=1000")
    assert response.status_code == 200
    users = response.json()
    assert isinstance(users, list)

    # Check if our test user is in the list
    test_user_found = any(user["username"] == test_user.username for user in users)
    assert test_user_found, (
        f"Test user '{test_user.username}' not found in users list of {len(users)} users"
    )


def should_return_filtered_users_when_status_provided(
    test_client: TestClient, test_user: UserORM
) -> None:
    response = test_client.get("/api/v1/users/?status=active")
    assert response.status_code == 200
    users = response.json()
    assert all(user["status"] == "active" for user in users)


def should_find_users_when_search_query_matches(
    test_client: TestClient, test_user: UserORM
) -> None:
    # Use the actual test user's username for search
    search_query = (
        test_user.username.split("_")[0] + "_api"
    )  # Extract base part for search
    response = test_client.get(f"/api/v1/users/search?query={search_query}")
    assert response.status_code == 200
    users = response.json()
    assert len(users) > 0
    # Check if our test user is in the search results
    assert any(user["username"] == test_user.username for user in users)


def should_return_empty_list_when_search_query_does_not_match(
    test_client: TestClient,
) -> None:
    response = test_client.get("/api/v1/users/search?query=non_existing_user")
    assert response.status_code == 200
    users = response.json()
    assert len(users) == 0


def should_update_user_status_when_valid_data(
    test_client: TestClient, test_user: UserORM, db: Session
) -> None:
    update_data = {"status": "inactive"}
    response = test_client.patch(
        f"/api/v1/users/{test_user.id}/status", json=update_data
    )
    assert response.status_code == 200
    assert response.json() is True

    # Verify the update in database - SQLAlchemy Enum field returns enum object
    # Refresh the session to see changes from other transactions
    db.expire_all()
    updated_db_user = db.query(UserORM).filter(UserORM.id == test_user.id).first()
    assert updated_db_user is not None
    assert updated_db_user.status == UserStatus.INACTIVE, (
        f"Expected UserStatus.INACTIVE but got '{updated_db_user.status}'"
    )

    # Verify the update via API - API should return string value
    response = test_client.get(f"/api/v1/users/{test_user.id}")
    assert response.status_code == 200
    user = response.json()
    assert user["status"] == "inactive", (
        f"Expected 'inactive' but got '{user['status']}'"
    )


def should_return_false_when_updating_non_existing_user(
    test_client: TestClient,
) -> None:
    update_data = {"status": "inactive"}
    non_existing_uuid = str(uuid.uuid4())
    response = test_client.patch(
        f"/api/v1/users/{non_existing_uuid}/status", json=update_data
    )
    assert response.status_code == 200
    assert response.json() is False


def should_return_user_when_valid_id_provided(
    test_client: TestClient, test_user: UserORM
) -> None:
    response = test_client.get(f"/api/v1/users/{test_user.id}")
    assert response.status_code == 200
    user = response.json()
    assert user["id"] == str(test_user.id)  # Convert UUID to string for comparison
    assert user["username"] == test_user.username
    assert user["email"] == test_user.email
    assert user["status"] == "active"


def should_return_404_when_user_not_found(test_client: TestClient) -> None:
    non_existing_uuid = str(uuid.uuid4())
    response = test_client.get(f"/api/v1/users/{non_existing_uuid}")
    assert response.status_code == 404
    assert response.json()["detail"] == f"User with ID {non_existing_uuid} not found"


def should_return_422_when_invalid_status_provided(
    test_client: TestClient, test_user: UserORM
) -> None:
    update_data = {"status": "INVALID_STATUS"}
    response = test_client.patch(
        f"/api/v1/users/{test_user.id}/status", json=update_data
    )
    assert response.status_code == 422
