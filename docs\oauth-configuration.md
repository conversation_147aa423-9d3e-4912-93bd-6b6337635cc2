# OAuth Configuration Guide

## Overview

The OAuth system has been redesigned to be more flexible and support multiple providers with dynamic callback URL generation.

## Configuration Changes

### Before (Fixed Redirect URI)
```bash
AI4SE_MCP_HUB_OAUTH_REDIRECT_URI=http://localhost:8000/api/v1/auth/oauth/callback
```

### After (Dynamic Base URL)
```bash
AI4SE_MCP_HUB_OAUTH_BASE_URL=http://localhost:8000
```

## How It Works

The system now dynamically generates provider-specific callback URLs:

- **GitHub**: `{base_url}/api/v1/auth/oauth/github/callback`
- **Google**: `{base_url}/api/v1/auth/oauth/google/callback`

## Environment Variables

```bash
# OAuth Base URL - used to generate provider-specific callback URLs
AI4SE_MCP_HUB_OAUTH_BASE_URL=http://your-domain.com

# GitHub OAuth App Configuration
AI4SE_MCP_HUB_GITHUB_CLIENT_ID=your-github-client-id
AI4SE_MCP_HUB_GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth App Configuration (optional)
AI4SE_MCP_HUB_GOOGLE_CLIENT_ID=your-google-client-id
AI4SE_MCP_HUB_GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## OAuth Provider Setup

### GitHub OAuth App
1. Go to GitHub Settings > Developer settings > OAuth Apps
2. Create a new OAuth App
3. Set Authorization callback URL to: `{your-base-url}/api/v1/auth/oauth/github/callback`
4. Copy Client ID and Client Secret to environment variables

### Google OAuth App
1. Go to Google Cloud Console > APIs & Services > Credentials
2. Create OAuth 2.0 Client ID
3. Add authorized redirect URI: `{your-base-url}/api/v1/auth/oauth/google/callback`
4. Copy Client ID and Client Secret to environment variables

## API Endpoints

### Generate Authorization URL
```http
GET /api/v1/auth/oauth/authorize/{provider}?state=optional-state
```

**Supported providers**: `github`, `google`

**Response**:
```json
{
  "authorization_url": "https://github.com/login/oauth/authorize?client_id=...",
  "provider": "github",
  "state": "optional-state"
}
```

### Handle OAuth Callback
```http
GET /api/v1/auth/oauth/{provider}/callback?code=auth-code&state=optional-state
```

**Response**:
```json
{
  "access_token": "jwt-token",
  "token_type": "bearer"
}
```

## Benefits

1. **Flexibility**: Easy to add new OAuth providers without changing configuration
2. **Environment-specific**: Different base URLs for development, staging, and production
3. **Provider isolation**: Each provider has its own callback endpoint
4. **Maintainability**: Single base URL configuration instead of multiple provider-specific URLs
