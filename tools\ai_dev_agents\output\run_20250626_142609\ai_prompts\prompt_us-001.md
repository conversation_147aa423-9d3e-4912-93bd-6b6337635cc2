# AI开发任务提示

## 任务概述
请基于以下用户故事实现完整的功能代码。

## 用户故事
**ID**: US-001
**标题**: 创建领域实体
**描述**: 作为系统用户，我希望能够创建新的领域实体，以便在系统中表示业务对象
**领域上下文**: 核心领域上下文
**优先级**: high

## 验收标准
- 创建实体时必须生成有效的UUID作为ID
- 创建实体时必须自动记录创建时间戳
- 创建实体时必须初始化修改时间戳等于创建时间

## 项目上下文
项目背景:
- 项目名称: 未知项目
- 项目描述: 无描述

技术架构:
- 基于FastAPI和DDD架构
- 使用SQLAlchemy作为ORM
- 遵循四层架构模式

项目规则:
- 严格遵循PEP 8代码规范
- 强制使用类型提示
- 所有注释和文档使用英文
- 按业务模块组织代码结构


## 实现要求
1. 严格遵循DDD四层架构
2. 在 `modules/核心领域上下文/` 目录下实现
3. 包含完整的接口层、应用层、领域层和基础设施层代码
4. 编写对应的单元测试和集成测试
5. 确保代码符合项目规范和质量标准

## 输出要求
请生成以下文件:
- 接口层: API路由和Pydantic模型
- 应用层: 应用服务和用例
- 领域层: 实体和仓库接口
- 基础设施层: 仓库实现和ORM模型
- 测试文件: 单元测试和集成测试

请确保代码质量高、可维护性强，并包含适当的错误处理和日志记录。
