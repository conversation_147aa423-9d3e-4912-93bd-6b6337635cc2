"""Add OAuth provider configuration table

Revision ID: e013f3aba6c2
Revises: 53b6b5772d85
Create Date: 2025-06-24 15:03:16.801193

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'e013f3aba6c2'
down_revision = '53b6b5772d85'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('oauth_providers',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=50), nullable=False),
    sa.Column('display_name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_enabled', sa.Boolean(), nullable=False),
    sa.Column('icon_url', sa.String(length=500), nullable=True),
    sa.Column('client_id', sa.String(length=255), nullable=False),
    sa.Column('client_secret', sa.String(length=255), nullable=False),
    sa.Column('authorize_url', sa.String(length=500), nullable=False),
    sa.Column('token_url', sa.String(length=500), nullable=False),
    sa.Column('user_info_url', sa.String(length=500), nullable=False),
    sa.Column('scope', sa.String(length=255), nullable=False),
    sa.Column('user_id_field', sa.String(length=50), nullable=False),
    sa.Column('email_field', sa.String(length=50), nullable=False),
    sa.Column('name_field', sa.String(length=50), nullable=False),
    sa.Column('username_field', sa.String(length=50), nullable=True),
    sa.Column('avatar_field', sa.String(length=50), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('oauth_providers', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_oauth_providers_name'), ['name'], unique=True)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('oauth_providers', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_oauth_providers_name'))

    op.drop_table('oauth_providers')
    # ### end Alembic commands ###
