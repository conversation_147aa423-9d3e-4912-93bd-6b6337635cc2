"""
Rules Loader for AI Development Agents

This module provides functionality to load and manage rule files for different
agent types and workflow steps.
"""

from pathlib import Path
from typing import Dict, List, Optional
import logging


class RulesLoader:
    """Loads and manages rule files for AI development agents."""
    
    def __init__(self, rules_dir: Optional[str] = None):
        """
        Initialize the rules loader.
        
        Args:
            rules_dir: Path to the rules directory. If None, uses default location.
        """
        if rules_dir is None:
            # Default to rules directory relative to this file
            self.rules_dir = Path(__file__).parent.parent / "rules"
        else:
            self.rules_dir = Path(rules_dir)
        
        self.logger = logging.getLogger("rules_loader")
        self._rule_cache: Dict[str, str] = {}
    
    def load_rule_file(self, rule_file: str) -> str:
        """
        Load a specific rule file.
        
        Args:
            rule_file: Name of the rule file (e.g., 'ddd_architecture.md')
            
        Returns:
            Content of the rule file, or empty string if not found
        """
        if rule_file in self._rule_cache:
            return self._rule_cache[rule_file]
        
        rule_path = self.rules_dir / rule_file
        
        if not rule_path.exists():
            self.logger.warning(f"Rule file not found: {rule_path}")
            return ""
        
        try:
            content = rule_path.read_text(encoding='utf-8')
            self._rule_cache[rule_file] = content
            return content
        except Exception as e:
            self.logger.error(f"Error loading rule file {rule_path}: {e}")
            return ""
    
    def get_rules_for_agent(self, agent_type: str) -> str:
        """
        Get combined rules for a specific agent type.
        
        Args:
            agent_type: Type of agent ('business_analyzer', 'domain_modeler', etc.)
            
        Returns:
            Combined rule content for the agent
        """
        rule_mapping = {
            'business_analyzer': [
                'ddd_architecture.md',
                'code_quality.md'
            ],
            'domain_modeler': [
                'ddd_architecture.md',
                'database_design.md'
            ],
            'requirements_generator': [
                'ddd_architecture.md',
                'code_quality.md',
                'api_design.md',
                'database_design.md',
                'testing.md'
            ],
            'prompt_builder': [
                'ddd_architecture.md',
                'code_quality.md',
                'api_design.md',
                'database_design.md',
                'testing.md'
            ]
        }
        
        rule_files = rule_mapping.get(agent_type, [])
        if not rule_files:
            self.logger.warning(f"No rules defined for agent type: {agent_type}")
            return ""
        
        combined_rules = []
        for rule_file in rule_files:
            content = self.load_rule_file(rule_file)
            if content:
                combined_rules.append(f"## {rule_file.replace('.md', '').replace('_', ' ').title()}\n\n{content}")
        
        return "\n\n---\n\n".join(combined_rules)
    
    def get_available_rules(self) -> List[str]:
        """
        Get list of available rule files.
        
        Returns:
            List of rule file names
        """
        if not self.rules_dir.exists():
            return []
        
        rule_files = []
        for file_path in self.rules_dir.iterdir():
            if file_path.is_file() and file_path.suffix == '.md' and file_path.name != 'README.md':
                rule_files.append(file_path.name)
        
        return sorted(rule_files)
    
    def clear_cache(self):
        """Clear the rule cache."""
        self._rule_cache.clear()
