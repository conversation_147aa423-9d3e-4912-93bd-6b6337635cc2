"""OAuth account domain models."""

from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, ConfigDict, Field, field_validator


class OAuthAccount(BaseModel):
    """OAuth account domain entity."""

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    user_id: UUID
    provider: str  # Dynamic provider name (e.g., 'github', 'google')
    provider_account_id: str
    created_at: datetime = Field(default_factory=datetime.now)

    @field_validator("provider")
    @classmethod
    def validate_provider(cls, v):
        """Validate that provider is not empty."""
        if not v or not v.strip():
            raise ValueError("Provider cannot be empty")
        return v

    @field_validator("provider_account_id")
    @classmethod
    def validate_provider_account_id(cls, v):
        """Validate that provider_account_id is not empty."""
        if not v or not v.strip():
            raise ValueError("Provider account ID cannot be empty")
        return v

    def __eq__(self, other):
        """Compare OAuth accounts based on their ID."""
        if not isinstance(other, OAuthAccount):
            return False
        return self.id == other.id

    def __hash__(self):
        """Hash based on OAuth account ID."""
        return hash(self.id)
