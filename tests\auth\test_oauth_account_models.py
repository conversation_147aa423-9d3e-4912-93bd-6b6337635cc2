"""Tests for OAuth account domain models."""

import uuid
from datetime import datetime

import pytest

from modules.auth.domain.oauth_account_models import OAuthAccount


class TestOAuthAccount:
    """Test cases for OAuthAccount domain model."""

    def should_create_oauth_account_with_valid_data(self):
        """Test that OAuthAccount can be created with valid data."""
        account_id = uuid.uuid4()
        user_id = uuid.uuid4()
        now = datetime.now()

        oauth_account = OAuthAccount(
            id=account_id,
            user_id=user_id,
            provider="github",
            provider_account_id="12345",
            created_at=now,
        )

        assert oauth_account.id == account_id
        assert oauth_account.user_id == user_id
        assert oauth_account.provider == "github"
        assert oauth_account.provider_account_id == "12345"
        assert oauth_account.created_at == now

    def should_create_oauth_account_with_default_timestamp_when_not_specified(self):
        """Test that OAuthAccount is created with current timestamp by default."""
        before_creation = datetime.now()

        oauth_account = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="github",
            provider_account_id="12345",
        )

        after_creation = datetime.now()

        assert before_creation <= oauth_account.created_at <= after_creation

    def should_support_different_oauth_providers(self):
        """Test that OAuthAccount supports different OAuth providers."""
        github_account = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="github",
            provider_account_id="github123",
        )

        google_account = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="google",
            provider_account_id="google456",
        )

        assert github_account.provider == "github"
        assert google_account.provider == "google"
        assert github_account.provider_account_id == "github123"
        assert google_account.provider_account_id == "google456"

    def should_require_non_empty_provider(self):
        """Test that provider cannot be empty."""
        with pytest.raises((ValueError, TypeError)):
            OAuthAccount(
                id=uuid.uuid4(),
                user_id=uuid.uuid4(),
                provider="",
                provider_account_id="12345",
            )

    def should_require_non_empty_provider_account_id(self):
        """Test that provider account ID cannot be empty."""
        with pytest.raises((ValueError, TypeError)):
            OAuthAccount(
                id=uuid.uuid4(),
                user_id=uuid.uuid4(),
                provider="github",
                provider_account_id="",
            )

    def should_require_valid_user_id(self):
        """Test that user ID must be a valid UUID."""
        with pytest.raises((ValueError, TypeError)):
            OAuthAccount(
                id=uuid.uuid4(),
                user_id=None,
                provider="github",
                provider_account_id="12345",
            )

    def should_require_valid_account_id(self):
        """Test that account ID must be a valid UUID."""
        with pytest.raises((ValueError, TypeError)):
            OAuthAccount(
                id=None,
                user_id=uuid.uuid4(),
                provider="github",
                provider_account_id="12345",
            )

    def should_have_string_representation(self):
        """Test that OAuthAccount has a meaningful string representation."""
        oauth_account = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="github",
            provider_account_id="12345",
        )

        str_repr = str(oauth_account)

        assert "github" in str_repr or "12345" in str_repr

    def should_support_equality_comparison(self):
        """Test that OAuthAccount supports equality comparison based on ID."""
        account_id = uuid.uuid4()
        user_id = uuid.uuid4()

        account1 = OAuthAccount(
            id=account_id,
            user_id=user_id,
            provider="github",
            provider_account_id="12345",
        )

        account2 = OAuthAccount(
            id=account_id,
            user_id=uuid.uuid4(),  # Different user ID
            provider="google",  # Different provider
            provider_account_id="67890",  # Different provider account ID
        )

        account3 = OAuthAccount(
            id=uuid.uuid4(),  # Different account ID
            user_id=user_id,
            provider="github",
            provider_account_id="12345",
        )

        assert account1 == account2  # Same account ID
        assert account1 != account3  # Different account ID

    def should_allow_same_user_to_have_multiple_oauth_accounts(self):
        """Test that same user can have multiple OAuth accounts for different providers."""
        user_id = uuid.uuid4()

        github_account = OAuthAccount(
            id=uuid.uuid4(),
            user_id=user_id,
            provider="github",
            provider_account_id="github123",
        )

        google_account = OAuthAccount(
            id=uuid.uuid4(),
            user_id=user_id,
            provider="google",
            provider_account_id="google456",
        )

        assert github_account.user_id == google_account.user_id
        assert github_account.provider != google_account.provider
        assert github_account.id != google_account.id

    def should_maintain_provider_account_id_uniqueness_per_provider(self):
        """Test that provider account ID should be unique per provider."""
        # This test documents the expected behavior - actual uniqueness
        # would be enforced at the repository/database level

        account1 = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="github",
            provider_account_id="12345",
        )

        account2 = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="github",
            provider_account_id="12345",  # Same provider account ID
        )

        # Both accounts can be created at the model level
        # but should be prevented at the repository level
        assert account1.provider_account_id == account2.provider_account_id
        assert account1.provider == account2.provider
        assert account1.id != account2.id  # Different account IDs

    def should_allow_same_provider_account_id_for_different_providers(self):
        """Test that same provider account ID can exist for different providers."""
        account1 = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="github",
            provider_account_id="12345",
        )

        account2 = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="google",
            provider_account_id="12345",  # Same provider account ID
        )

        assert account1.provider_account_id == account2.provider_account_id
        assert account1.provider != account2.provider
        assert account1.id != account2.id

    def should_preserve_creation_timestamp(self):
        """Test that creation timestamp is preserved and immutable."""
        creation_time = datetime.now()

        oauth_account = OAuthAccount(
            id=uuid.uuid4(),
            user_id=uuid.uuid4(),
            provider="github",
            provider_account_id="12345",
            created_at=creation_time,
        )

        assert oauth_account.created_at == creation_time

        # Creation timestamp should not change
        original_created_at = oauth_account.created_at

        # Simulate some operation that might modify the object
        oauth_account.provider_account_id = "67890"

        assert oauth_account.created_at == original_created_at
