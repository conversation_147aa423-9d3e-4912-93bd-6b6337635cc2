"""
Orchestrator for the 6-step AI development workflow.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

from .base_agent import BaseAgent, AgentResult, WorkflowContext
from .xml_schemas import XMLParser, BusinessAnalysis, UserStory
from .workflow_logger import WorkflowLogger


class Orchestrator:
    """Orchestrator for the 6-step workflow."""

    def __init__(self, agents: Dict[str, BaseAgent], verbose: bool = False, llm_config: Optional[Dict[str, Any]] = None):
        """Initialize orchestrator with agents."""
        self.agents = agents
        self.verbose = verbose
        # Store LLM configuration for display
        self.llm_config = llm_config or {
            "provider": "unknown",
            "model": "unknown",
            "temperature": 0.7,
            "max_tokens": 4000,
            "streaming": True,
            "timeout": 60
        }

        # Initialize with default context - will be updated during execution
        self.context = WorkflowContext(
            project_root=".",
            project_rules={},
            existing_modules=[],
            tech_stack=["FastAPI", "SQLAlchemy", "Pydantic"],
            architecture_style="DDD"
        )
        self.results: Dict[str, AgentResult] = {}
        self.logger = logging.getLogger(__name__)
        self.workflow_logger = None

        # Initialize stream displayer and LLM (will be set by CLI)
        self.stream_displayer = None
        self.llm = None

        # Validate required agents
        required_agents = [
            "business_analyzer",
            "domain_modeler",
            "requirements_analyzer",
            "technical_leader",
            "result_generator",
            "presentation_generator"
        ]

        for agent_name in required_agents:
            if agent_name not in agents:
                raise ValueError(f"Required agent '{agent_name}' not found")

    def get_llm_info(self) -> Dict[str, Any]:
        """Get LLM configuration information for display."""
        return {
            "provider": self.llm_config.get("provider", "unknown"),
            "model": self.llm_config.get("model", "unknown"),
            "temperature": self.llm_config.get("temperature", 0.7),
            "max_tokens": self.llm_config.get("max_tokens", 4000),
            "streaming": self.llm_config.get("streaming", True),
            "timeout": self.llm_config.get("timeout", 60)
        }

    def create_context(self, project_root: str, project_rules: str = "") -> WorkflowContext:
        """Create workflow context with project information."""
        # Update the existing context with new project information
        self.context.project_root = project_root
        if project_rules:
            self.context.rules_content = project_rules

        # Detect existing modules (simplified implementation)
        project_path = Path(project_root)
        existing_modules = []
        if project_path.exists():
            # Look for common module directories
            for item in project_path.iterdir():
                if item.is_dir() and not item.name.startswith('.'):
                    existing_modules.append(item.name)

        self.context.existing_modules = existing_modules
        return self.context
    
    def execute_workflow(self, prd_content: str, rules_content: str, output_path: Path) -> Dict[str, Any]:
        """Execute the complete 7-step workflow with rules processing."""
        # Initialize workflow logger
        self.workflow_logger = WorkflowLogger(output_path, self.verbose)

        self.logger.info("Starting AI development workflow")
        self.workflow_logger.log_step_start(0, "Workflow Initialization", {
            "prd_length": len(prd_content),
            "rules_length": len(rules_content),
            "output_path": str(output_path)
        })

        # Initialize context
        self.context.prd_content = prd_content
        self.context.rules_content = rules_content
        self.context.output_path = output_path
        self.context.start_time = datetime.now()

        workflow_results = {
            "success": False,
            "steps_completed": 0,
            "total_steps": 7,  # Updated to 7 steps including rules processing
            "results": {},
            "errors": [],
            "execution_time": 0
        }

        try:
            # Step 0: Rules Processing (if rules provided)
            processed_rules_content = rules_content
            if rules_content and rules_content.strip():
                print(f"\n[STEP 0] 规则处理 - 使用LLM分析和优化规则内容...")
                self.logger.info("Step 0: Processing rules with LLM")
                self.workflow_logger.log_step_start(0, "Rules Processing", {
                    "rules_content": f"String({len(rules_content)} chars)"
                })

                rules_result = self._execute_rules_processing(rules_content, output_path)
                if rules_result.success:
                    processed_rules_content = rules_result.data.get("processed_rules", rules_content)
                    self.workflow_logger.log_step_complete(0, rules_result.data)
                    workflow_results["results"]["rules_processing"] = rules_result.data
                    print(f"[SUCCESS] 规则处理完成，生成了优化后的规则内容")
                else:
                    print(f"[WARNING] 规则处理失败，使用原始规则内容: {'; '.join(rules_result.errors)}")
                    self.workflow_logger.log_step_complete(0, errors=rules_result.errors)
            else:
                print(f"[INFO] 未提供规则内容，跳过规则处理步骤")

            # Step 1: Business Analysis
            print(f"\n[STEP 1] 业务分析 - 分析PRD文档并提取业务需求...")
            self.logger.info("Step 1: Executing business analysis")
            self.workflow_logger.log_step_start(1, "Business Analysis", {
                "prd_content": f"String({len(prd_content)} chars)",
                "rules_content": f"String({len(processed_rules_content)} chars)"
            })

            business_result = self._execute_business_analysis(prd_content, processed_rules_content)
            if not business_result.success:
                self.workflow_logger.log_step_complete(1, errors=business_result.errors)
                raise Exception(f"Business analysis failed: {'; '.join(business_result.errors)}")

            self.workflow_logger.log_step_complete(1, business_result.data)
            self.results["business_analyzer"] = business_result
            workflow_results["steps_completed"] = 1
            workflow_results["results"]["business_analysis"] = business_result.data
            print(f"[SUCCESS] 业务分析完成，识别了 {business_result.data.get('user_stories_count', 0)} 个用户故事")

            # Step 2: Domain Modeling
            print(f"\n[STEP 2] 领域建模 - 基于业务分析进行DDD领域建模...")
            self.logger.info("Step 2: Executing domain modeling")
            self.workflow_logger.log_step_start(2, "Domain Modeling", business_result.data)

            domain_result = self._execute_domain_modeling(business_result.data)
            if not domain_result.success:
                self.workflow_logger.log_step_complete(2, errors=domain_result.errors)
                raise Exception(f"Domain modeling failed: {'; '.join(domain_result.errors)}")

            self.workflow_logger.log_step_complete(2, domain_result.data)
            self.results["domain_modeler"] = domain_result
            workflow_results["steps_completed"] = 2
            workflow_results["results"]["domain_model"] = domain_result.data
            print(f"[SUCCESS] 领域建模完成，定义了 {len(domain_result.data.get('bounded_contexts', []))} 个限界上下文")

            # Step 3: Requirements Analysis
            print(f"\n[STEP 3] 需求分析 - 拆分用户故事并按领域上下文分组...")
            self.logger.info("Step 3: Executing requirements analysis")
            self.workflow_logger.log_step_start(3, "Requirements Analysis", {
                "business_analysis": business_result.data,
                "domain_model": domain_result.data
            })

            requirements_result = self._execute_requirements_analysis(
                business_result.data, domain_result.data
            )
            if not requirements_result.success:
                self.workflow_logger.log_step_complete(3, errors=requirements_result.errors)
                raise Exception(f"Requirements analysis failed: {'; '.join(requirements_result.errors)}")

            self.workflow_logger.log_step_complete(3, requirements_result.data)
            self.results["requirements_analyzer"] = requirements_result
            workflow_results["steps_completed"] = 3
            workflow_results["results"]["requirements"] = requirements_result.data
            print(f"[SUCCESS] 需求分析完成，生成了详细的需求规格说明")

            # Step 4: Quality Review (with retry mechanism)
            print(f"\n[STEP 4] 质量审查 - 技术负责人审查需求质量...")
            self.logger.info("Step 4: Executing quality review")
            self.workflow_logger.log_step_start(4, "Quality Review", requirements_result.data)

            quality_result, final_requirements = self._execute_quality_review_with_retry(
                requirements_result.data, max_retries=2
            )
            if not quality_result.success:
                self.workflow_logger.log_step_complete(4, errors=quality_result.errors)
                raise Exception(f"Quality review failed: {'; '.join(quality_result.errors)}")

            self.workflow_logger.log_step_complete(4, quality_result.data)
            self.results["technical_leader"] = quality_result
            workflow_results["steps_completed"] = 4
            workflow_results["results"]["quality_review"] = quality_result.data
            workflow_results["results"]["final_requirements"] = final_requirements
            print(f"[SUCCESS] 质量审查完成，需求已通过技术负责人审核")

            # Step 5: Result Generation
            print(f"\n[STEP 5] 结果生成 - 生成开发文档和AI提示词...")
            self.logger.info("Step 5: Executing result generation")
            self.workflow_logger.log_step_start(5, "Result Generation", final_requirements)

            result_generation = self._execute_result_generation(final_requirements)
            if not result_generation.success:
                self.workflow_logger.log_step_complete(5, errors=result_generation.errors)
                raise Exception(f"Result generation failed: {'; '.join(result_generation.errors)}")

            self.workflow_logger.log_step_complete(5, result_generation.data)
            self.results["result_generator"] = result_generation
            workflow_results["steps_completed"] = 5
            workflow_results["results"]["generated_results"] = result_generation.data
            print(f"[SUCCESS] 结果生成完成，已生成开发文档和AI提示词")

            # Step 6: HTML Presentation
            print(f"\n[STEP 6] 报告生成 - 生成HTML格式的工作流程报告...")
            self.logger.info("Step 6: Generating HTML presentation")
            self.workflow_logger.log_step_start(6, "HTML Presentation", workflow_results["results"])

            presentation_result = self._execute_presentation_generation(workflow_results["results"])
            if not presentation_result.success:
                self.workflow_logger.log_step_complete(6, errors=presentation_result.errors)
                raise Exception(f"Presentation generation failed: {'; '.join(presentation_result.errors)}")

            self.workflow_logger.log_step_complete(6, presentation_result.data)
            self.results["presentation_generator"] = presentation_result
            workflow_results["steps_completed"] = 6
            workflow_results["results"]["presentation"] = presentation_result.data
            print(f"[SUCCESS] HTML报告生成完成")

            # Mark as successful
            workflow_results["success"] = True
            self.logger.info("Workflow completed successfully")
            print(f"\n[COMPLETE] 🎉 AI开发工作流程全部完成！共完成 {workflow_results['steps_completed']} 个步骤")
            
        except Exception as e:
            self.logger.error(f"Workflow failed at step {workflow_results['steps_completed'] + 1}: {e}")
            workflow_results["errors"].append(str(e))
        
        finally:
            # Calculate execution time
            if hasattr(self.context, 'start_time'):
                execution_time = (datetime.now() - self.context.start_time).total_seconds()
                workflow_results["execution_time"] = execution_time
            
            # Save workflow summary
            self._save_workflow_summary(workflow_results, output_path)
        
        return workflow_results

    def _execute_rules_processing(self, rules_content: str, output_path: Path) -> AgentResult:
        """
        Execute rules processing step.

        使用LLM分析和优化用户提供的规则内容
        - 输入: 原始规则内容
        - 处理: LLM分析、理解、重组规则
        - 输出: 优化后的规则内容
        """
        try:
            from ..agents.rules_processor import RulesProcessorAgent

            # Create rules processor agent with streaming support
            rules_agent = RulesProcessorAgent(
                llm=self.llm,
                verbose=self.verbose,
                stream_displayer=self.stream_displayer
            )

            input_data = {
                "rules_content": rules_content,
                "output_dir": str(output_path)
            }

            return rules_agent.process(input_data, self.context)

        except Exception as e:
            self.logger.error(f"Rules processing failed: {e}")
            return AgentResult(
                success=False,
                data={},
                errors=[f"Rules processing failed: {str(e)}"]
            )

    def _execute_business_analysis(self, prd_content: str, rules_content: str = "") -> AgentResult:
        """
        Execute business analysis step.

        根据设计文档步骤1: 业务分析 (Business Analysis)
        - 输入: PRD文档 + 项目规则约束
        - 处理: 深度理解PRD，生成系统需求文档
        - 输出: XML格式的业务需求分析报告
        """
        agent = self.agents["business_analyzer"]

        # Prepare input data according to design specification
        input_data = {
            "prd_content": prd_content,
            "rules_content": rules_content,
            "output_format": "xml"
        }

        return agent.process(input_data, self.context)
    
    def _execute_domain_modeling(self, business_analysis: Dict[str, Any]) -> AgentResult:
        """
        Execute domain modeling step.

        根据设计文档步骤2: 领域建模 (Domain Modeling)
        - 输入: 业务需求分析报告
        - 处理: 进行领域建模、数据库建模、API建模
        - 输出: XML格式的领域模型设计
        """
        agent = self.agents["domain_modeler"]
        return agent.process(business_analysis, self.context)

    def _execute_requirements_analysis(self, business_analysis: Dict[str, Any],
                                     domain_model: Dict[str, Any]) -> AgentResult:
        """
        Execute requirements analysis step.

        根据设计文档步骤3: 需求分析 (Requirements Analysis)
        - 输入: 业务分析 + 领域建模结果
        - 处理: 拆分用户故事，按领域上下文分组
        - 输出: XML格式的用户故事集合
        """
        agent = self.agents["requirements_analyzer"]

        input_data = {
            "business_analysis": business_analysis,
            "domain_model": domain_model
        }

        return agent.process(input_data, self.context)
    
    def _execute_quality_review_with_retry(self, requirements: Dict[str, Any],
                                         max_retries: int = 2) -> tuple[AgentResult, Dict[str, Any]]:
        """
        Execute quality review with retry mechanism.

        根据设计文档步骤4: 质量检查 (Quality Review)
        - 输入: 用户故事集合
        - 处理: 检查用户故事完整性和质量，提出改进建议
        - 输出: XML格式的质量检查报告和改进建议
        """
        technical_leader = self.agents["technical_leader"]
        requirements_analyzer = self.agents["requirements_analyzer"]
        
        current_requirements = requirements
        
        for attempt in range(max_retries + 1):
            self.logger.info(f"Quality review attempt {attempt + 1}")
            
            # Review current requirements
            review_result = technical_leader.process(current_requirements, self.context)
            
            if not review_result.success:
                return review_result, current_requirements
            
            # Check if requirements are approved
            review_data = review_result.data
            if review_data.get("approved", False):
                self.logger.info("Requirements approved by technical leader")
                return review_result, current_requirements
            
            # If not approved and we have retries left, regenerate requirements
            if attempt < max_retries:
                self.logger.info("Requirements need improvement, regenerating...")
                improvement_suggestions = review_data.get("improvement_suggestions", [])
                
                # Add improvement context to requirements analyzer
                regeneration_input = {
                    "original_requirements": current_requirements,
                    "improvement_suggestions": improvement_suggestions,
                    "business_analysis": self.results["business_analyzer"].data,
                    "domain_model": self.results["domain_modeler"].data
                }
                
                regeneration_result = requirements_analyzer.process(regeneration_input, self.context)
                if regeneration_result.success:
                    current_requirements = regeneration_result.data
                else:
                    self.logger.warning("Failed to regenerate requirements")
                    break
        
        # Return the last review result and current requirements
        return review_result, current_requirements
    
    def _execute_result_generation(self, final_requirements: Dict[str, Any]) -> AgentResult:
        """
        Execute result generation step.

        根据设计文档步骤5: 结果生成 (Result Generation)
        - 输入: 审核通过的用户故事
        - 处理: 生成最终的开发需求和AI提示词
        - 输出: 结构化的开发文档和提示词
        """
        agent = self.agents["result_generator"]
        return agent.process(final_requirements, self.context)

    def _execute_presentation_generation(self, all_results: Dict[str, Any]) -> AgentResult:
        """
        Execute presentation generation step.

        根据设计文档步骤6: HTML展示 (HTML Presentation)
        - 输入: 所有步骤的输出结果
        - 处理: 生成可视化的HTML报告页面
        - 输出: 完整的HTML展示页面
        """
        agent = self.agents["presentation_generator"]
        
        # Prepare comprehensive input for presentation
        presentation_input = {
            "workflow_results": all_results,
            "execution_context": {
                "start_time": self.context.start_time.isoformat() if hasattr(self.context, 'start_time') else None,
                "total_steps": 6,
                "completed_steps": len(all_results)
            },
            "agent_results": {name: result.data for name, result in self.results.items()}
        }
        
        return agent.process(presentation_input, self.context)

    def _save_workflow_summary(self, workflow_results: Dict[str, Any], output_path: Path):
        """Save workflow summary to file."""
        try:
            summary_file = output_path / "workflow_summary.json"
            summary_file.parent.mkdir(parents=True, exist_ok=True)

            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(workflow_results, f, indent=2, ensure_ascii=False, default=str)

            self.logger.info(f"Workflow summary saved to {summary_file}")
        except Exception as e:
            self.logger.error(f"Failed to save workflow summary: {e}")

    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics."""
        stats = {
            "total_agents": len(self.agents),
            "completed_steps": len(self.results),
            "success_rate": len([r for r in self.results.values() if r.success]) / len(self.results) if self.results else 0,
            "agent_performance": {}
        }

        for name, result in self.results.items():
            stats["agent_performance"][name] = {
                "success": result.success,
                "execution_time": getattr(result, 'execution_time', 0),
                "errors": result.errors if not result.success else []
            }

        return stats

    def _execute_requirements_generation(self, domain_data: Dict[str, Any]) -> AgentResult:
        """
        Execute requirements generation step (for CLI compatibility).

        This method provides compatibility with CLI commands that need to generate
        requirements from domain model data.
        """
        # This is essentially the same as _execute_requirements_analysis
        # but with different input format for CLI compatibility
        agent = self.agents["requirements_analyzer"]

        # Prepare input data - domain_data should contain both business and domain info
        input_data = domain_data

        return agent.process(input_data, self.context)

    def _execute_prompt_building(self, requirements_data: Dict[str, Any]) -> AgentResult:
        """
        Execute prompt building step (for CLI compatibility).

        This method provides compatibility with CLI commands that need to build
        AI prompts from requirements data.
        """
        # This is essentially the same as _execute_result_generation
        agent = self.agents["result_generator"]
        return agent.process(requirements_data, self.context)

    def execute_full_workflow(
        self,
        prd_content: str,
        project_root: str,
        output_dir: str = "output",
        selected_modules: Optional[List[str]] = None,
        selected_rules: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Execute the complete AI development workflow with enhanced features.

        This method adapts the existing execute_workflow to support the CLI interface
        with additional features like module selection and rules processing.
        """
        try:
            # Create timestamped output directory
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = Path(output_dir) / f"run_{timestamp}"
            output_path.mkdir(parents=True, exist_ok=True)

            # Prepare rules content
            rules_content = ""
            if selected_rules:
                # Combine all selected rules files
                for rules_file in selected_rules:
                    try:
                        with open(rules_file, 'r', encoding='utf-8') as f:
                            rules_content += f"\n\n# Rules from {rules_file}\n\n"
                            rules_content += f.read()
                    except Exception as e:
                        self.logger.warning(f"Failed to read rules file {rules_file}: {e}")

            # Execute the core workflow
            workflow_result = self.execute_workflow(prd_content, rules_content, output_path)

            # Enhance result with additional information
            enhanced_result = {
                "success": workflow_result.get("success", False),
                "output_directory": str(output_path),
                "summary_file": str(output_path / "workflow_summary.json"),
                "modules": {},
                "error": workflow_result.get("errors", []),
                "details": workflow_result.get("errors", [])
            }

            # If successful, add module information
            if workflow_result.get("success"):
                # For now, create a simple module structure
                # This can be enhanced based on actual workflow results
                if selected_modules:
                    for module_name in selected_modules:
                        enhanced_result["modules"][module_name] = {
                            "files": {
                                "requirements": f"{module_name}_requirements.md",
                                "prompt": f"{module_name}_ai_prompt.md"
                            }
                        }
                else:
                    # Default module if none specified
                    enhanced_result["modules"]["default"] = {
                        "files": {
                            "requirements": "requirements.md",
                            "prompt": "ai_prompt.md"
                        }
                    }

            return enhanced_result

        except Exception as e:
            self.logger.error(f"Full workflow execution failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "details": [str(e)],
                "output_directory": "",
                "summary_file": "",
                "modules": {}
            }
