# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
build/
dist/
wheels/
*.egg-info/
*.egg

# Virtual environments
.venv/
venv/
env/

# Environment files
.env
.env.local
.env*.local
/uvicorn.pid

# IDE / Editor
.vscode/
.idea/

# Testing
.coverage
htmlcov/
.pytest_cache/

# Logs
*.log

# Local database
*.sqlite3

# ===== 新增 UV 相关配置 =====
# UV 缓存目录
.uv_cache/

# UV 脚本日志
uv_scripts.log

# UVicorn 进程文件
uvicorn.pid

# 构建产物
dist/
build/
*.egg-info/
