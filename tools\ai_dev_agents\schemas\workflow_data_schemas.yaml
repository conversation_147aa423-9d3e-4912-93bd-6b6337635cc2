# AI Development Workflow Data Schemas (JSON Format)
# 定义工作流各步骤间的标准JSON数据结构

# 业务分析结果数据结构 (JSON)
business_analysis_schema:
  type: object
  properties:
    content_type:
      type: string
      enum: ["markdown"]
      description: "内容格式类型"
    raw_markdown:
      type: string
      description: "原始Markdown格式的业务分析内容"
    sections:
      type: object
      description: "提取的结构化章节"
      properties:
        business_overview:
          type: object
          properties:
            project_name: {type: string}
            project_description: {type: string}
            core_objectives: {type: array, items: {type: string}}
            target_users: {type: array, items: {type: string}}
            value_proposition: {type: array, items: {type: string}}
        core_entities:
          type: array
          items:
            type: object
            properties:
              name: {type: string}
              description: {type: string}
              key_attributes: {type: array, items: {type: string}}
              business_rules: {type: array, items: {type: string}}
              relationships: {type: array, items: {type: string}}
        functional_requirements:
          type: array
          items:
            type: object
            properties:
              id: {type: string}
              title: {type: string}
              description: {type: string}
              priority: {type: string, enum: ["high", "medium", "low"]}
              acceptance_criteria: {type: array, items: {type: string}}
        business_rules:
          type: array
          items:
            type: object
            properties:
              id: {type: string}
              category: {type: string}
              rule: {type: string}
              trigger_condition: {type: string}
              execution_action: {type: string}

# 领域建模结果数据结构
domain_modeling_schema:
  type: object
  properties:
    content_type:
      type: string
      enum: ["yaml"]
      description: "内容格式类型"
    concept_analysis:
      type: object
      description: "概念分析与合并建议"
      properties:
        similar_concepts:
          type: array
          items:
            type: object
            properties:
              concept_group: {type: string}
              similar_terms: {type: array, items: {type: string}}
              merge_suggestions: {type: array}
              recommended_approach: {type: string}
              final_concept_name: {type: string}
    bounded_contexts:
      type: array
      items:
        type: object
        properties:
          name: {type: string}
          description: {type: string}
          responsibilities: {type: array, items: {type: string}}
          relationships: {type: array}
    aggregates:
      type: array
      items:
        type: object
        properties:
          name: {type: string}
          context: {type: string}
          aggregate_root: {type: string}
          entities: {type: array, items: {type: string}}
          value_objects: {type: array, items: {type: string}}
          business_rules: {type: array, items: {type: string}}
          invariants: {type: array, items: {type: string}}
    domain_entities:
      type: array
      items:
        type: object
        properties:
          name: {type: string}
          aggregate: {type: string}
          description: {type: string}
          attributes: {type: array}
          business_methods: {type: array}
          business_rules: {type: array, items: {type: string}}
    value_objects:
      type: array
      items:
        type: object
        properties:
          name: {type: string}
          description: {type: string}
          attributes: {type: array}
          validation_rules: {type: array, items: {type: string}}
          immutable: {type: boolean, default: true}
    domain_services:
      type: array
      items:
        type: object
        properties:
          name: {type: string}
          context: {type: string}
          description: {type: string}
          methods: {type: array}
          dependencies: {type: array, items: {type: string}}
    repositories:
      type: array
      items:
        type: object
        properties:
          name: {type: string}
          managed_aggregate: {type: string}
          description: {type: string}
          methods: {type: array}
    domain_events:
      type: array
      items:
        type: object
        properties:
          name: {type: string}
          description: {type: string}
          trigger_conditions: {type: array, items: {type: string}}
          event_data: {type: array}
          handlers: {type: array, items: {type: string}}

# 技术需求生成结果数据结构
requirements_generation_schema:
  type: object
  properties:
    content_type:
      type: string
      enum: ["yaml"]
      description: "内容格式类型"
    module_requirements:
      type: object
      properties:
        module_name: {type: string}
        module_description: {type: string}
        priority: {type: string, enum: ["high", "medium", "low"]}
        dependencies: {type: array, items: {type: string}}
    technical_specifications:
      type: object
      properties:
        architecture_layer_requirements: {type: array}
        api_specifications: {type: array}
        data_model_requirements: {type: array}
        integration_requirements: {type: array}
    implementation_plan:
      type: object
      properties:
        development_phases: {type: array}
        file_structure: {type: object}
        coding_guidelines: {type: array}
    quality_requirements:
      type: object
      properties:
        testing_requirements: {type: array}
        performance_requirements: {type: array}
        security_requirements: {type: array}
        documentation_requirements: {type: array}

# AI提示词构建结果数据结构
prompt_building_schema:
  type: object
  properties:
    content_type:
      type: string
      enum: ["markdown"]
      description: "内容格式类型"
    prompt_metadata:
      type: object
      properties:
        title: {type: string}
        description: {type: string}
        target_module: {type: string}
        complexity_level: {type: string}
        estimated_time: {type: string}
        generation_timestamp: {type: string}
    task_description:
      type: object
      properties:
        overview: {type: string}
        objectives: {type: array, items: {type: string}}
        deliverables: {type: array, items: {type: string}}
        acceptance_criteria: {type: array, items: {type: string}}
    project_context:
      type: object
      properties:
        architecture_style: {type: string}
        tech_stack: {type: array, items: {type: string}}
        existing_modules: {type: array, items: {type: string}}
        coding_standards: {type: object}
        project_rules: {type: string, description: "动态生成的项目规则"}
    implementation_guidance:
      type: object
      properties:
        development_steps: {type: array}
        code_structure: {type: object}
        best_practices: {type: array}
    quality_requirements:
      type: object
      properties:
        code_quality: {type: object}
        testing: {type: object}
        documentation: {type: object}
    examples_and_templates:
      type: object
      properties:
        code_examples: {type: array}
        file_templates: {type: array}
        naming_examples: {type: object}

# 工作流上下文数据结构
workflow_context_schema:
  type: object
  properties:
    run_id: {type: string}
    timestamp: {type: string}
    input_file: {type: string}
    selected_rules: {type: array, items: {type: string}}
    processed_rules: {type: string, description: "处理后的规则内容"}
    current_step: {type: string}
    step_results:
      type: object
      properties:
        rules_processing: {$ref: "#/processed_rules_schema"}
        business_analysis: {$ref: "#/business_analysis_schema"}
        domain_modeling: {$ref: "#/domain_modeling_schema"}
        requirements_generation: {$ref: "#/requirements_generation_schema"}
        prompt_building: {$ref: "#/prompt_building_schema"}

# 规则处理结果数据结构
processed_rules_schema:
  type: object
  properties:
    content_type:
      type: string
      enum: ["markdown"]
      description: "内容格式类型"
    raw_markdown:
      type: string
      description: "处理后的规则Markdown内容"
    rule_categories:
      type: object
      properties:
        core_principles: {type: array, items: {type: string}}
        technical_specifications: {type: array, items: {type: string}}
        architectural_constraints: {type: array, items: {type: string}}
        coding_standards: {type: array, items: {type: string}}
        quality_requirements: {type: array, items: {type: string}}
        engineering_practices: {type: array, items: {type: string}}
        constraints_and_limitations: {type: array, items: {type: string}}
