version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: ai4se-mcp-hub-db
    environment:
      POSTGRES_USER: ai4se
      POSTGRES_PASSWORD: ai4se
      POSTGRES_DB: ai4se-mcp-hub
    ports:
      - "15432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ai4se"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7.2-bookworm
    container_name: ai4se-mcp-hub-redis
    ports:
      - "16379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  pgadmin:
    image: dpage/pgadmin4
    container_name: ai4se-mcp-hub-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "15050:80"
    depends_on:
      postgres:
        condition: service_healthy

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: ai4se-mcp-hub-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "18081:8081"
    depends_on:
      redis:
        condition: service_healthy

volumes:
  postgres_data:
  redis_data: