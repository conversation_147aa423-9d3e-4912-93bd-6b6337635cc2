# AI开发工作流改进设计方案

## 1. 新的6步工作流程

### 步骤1: 业务分析 (Business Analysis)
- **输入**: PRD文档 + 项目规则约束
- **处理**: 深度理解PRD，生成系统需求文档
- **输出**: XML格式的业务需求分析报告
- **Agent**: BusinessAnalyzerAgent

### 步骤2: 领域建模 (Domain Modeling)  
- **输入**: 业务需求分析报告
- **处理**: 进行领域建模、数据库建模、API建模
- **输出**: XML格式的领域模型设计
- **Agent**: DomainModelerAgent

### 步骤3: 需求分析 (Requirements Analysis)
- **输入**: 业务分析 + 领域建模结果
- **处理**: 拆分用户故事，按领域上下文分组
- **输出**: XML格式的用户故事集合
- **Agent**: RequirementsAnalyzerAgent

### 步骤4: 质量检查 (Quality Review)
- **输入**: 用户故事集合
- **处理**: 检查用户故事完整性和质量，提出改进建议
- **输出**: XML格式的质量检查报告和改进建议
- **Agent**: TechnicalLeaderAgent

### 步骤5: 结果生成 (Result Generation)
- **输入**: 审核通过的用户故事
- **处理**: 生成最终的开发需求和AI提示词
- **输出**: 结构化的开发文档和提示词
- **Agent**: ResultGeneratorAgent

### 步骤6: HTML展示 (HTML Presentation)
- **输入**: 所有步骤的输出结果
- **处理**: 生成可视化的HTML报告页面
- **输出**: 完整的HTML展示页面
- **Agent**: PresentationGeneratorAgent

## 2. XML数据格式规范

### 业务分析输出格式
```xml
<business_analysis>
    <project_info>
        <name>项目名称</name>
        <description>项目描述</description>
        <objectives>
            <objective>目标1</objective>
            <objective>目标2</objective>
        </objectives>
    </project_info>
    <functional_requirements>
        <requirement id="FR-001" priority="high">
            <title>需求标题</title>
            <description>需求描述</description>
            <acceptance_criteria>
                <criterion>验收标准1</criterion>
                <criterion>验收标准2</criterion>
            </acceptance_criteria>
        </requirement>
    </functional_requirements>
    <user_stories>
        <story id="US-001" domain_context="用户管理">
            <title>用户故事标题</title>
            <description>作为...我希望...以便...</description>
            <acceptance_criteria>
                <criterion>验收标准1</criterion>
                <criterion>验收标准2</criterion>
            </acceptance_criteria>
            <priority>high</priority>
        </story>
    </user_stories>
</business_analysis>
```

### 领域建模输出格式
```xml
<domain_model>
    <bounded_contexts>
        <context name="用户管理">
            <description>用户相关的业务逻辑</description>
            <entities>
                <entity name="User">
                    <attributes>
                        <attribute name="id" type="UUID">用户ID</attribute>
                        <attribute name="username" type="string">用户名</attribute>
                    </attributes>
                </entity>
            </entities>
        </context>
    </bounded_contexts>
    <database_design>
        <table name="users">
            <column name="id" type="UUID" primary_key="true"/>
            <column name="username" type="VARCHAR(50)" unique="true"/>
        </table>
    </database_design>
    <api_design>
        <endpoint path="/api/users" method="GET">
            <description>获取用户列表</description>
            <response_schema>UserListResponse</response_schema>
        </endpoint>
    </api_design>
</domain_model>
```

## 3. Agent职责重新定义

### BusinessAnalyzerAgent
- 深度理解PRD文档
- 提取功能需求和非功能需求
- 初步识别用户故事
- 输出结构化的业务分析报告

### DomainModelerAgent  
- 基于业务分析进行DDD建模
- 设计数据库模型
- 设计API接口
- 输出完整的技术架构设计

### RequirementsAnalyzerAgent
- 基于业务分析和领域建模拆分详细用户故事
- 按领域上下文对用户故事分组
- 确保用户故事符合INVEST原则
- 输出结构化的用户故事集合

### TechnicalLeaderAgent (新增)
- 审核用户故事的完整性和质量
- 检查是否符合项目规则约束
- 提出改进建议
- 决定是否需要重新生成

### ResultGeneratorAgent (新增)
- 基于审核通过的用户故事生成最终开发文档
- 生成配套的AI开发提示词
- 确保输出内容可直接用于AI开发

### PresentationGeneratorAgent (新增)
- 生成HTML展示页面
- 包含任务概要、统计信息、详细内容
- 提供友好的用户界面

## 4. 规则文件使用方式

- 规则文件作为系统提示词的一部分，指导各Agent生成符合项目要求的内容
- 不直接填充到最终的AI开发提示词中
- 在每个Agent的系统提示词中包含相关规则约束
- 确保生成的内容自然地体现规则要求

## 5. 数据流转机制

1. **输入验证**: 每个Agent验证输入数据的XML格式
2. **数据传递**: 使用标准化的XML Schema进行数据传递
3. **错误处理**: 包含详细的错误信息和重试机制
4. **版本控制**: 每个输出包含版本信息和生成时间戳

## 6. 质量保证机制

1. **XML Schema验证**: 确保所有输出符合预定义格式
2. **内容完整性检查**: 验证必要字段的存在和合理性
3. **一致性检查**: 确保不同步骤输出的一致性
4. **人工审核点**: 在关键步骤提供人工干预机会
