# Database configuration
AI4SE_MCP_HUB_DB_URL=postgresql://username:password@localhost:port/database

# Redis configuration
AI4SE_MCP_HUB_REDIS_URL=redis://localhost:port

# Application secret key (generate a secure value)
AI4SE_MCP_HUB_SECRET_KEY=your-secret-key-here

# API documentation settings
AI4SE_MCP_HUB_DEBUG=False  # Set to True to enable API documentation access

# OAuth Configuration
# GitHub OAuth App settings
AI4SE_MCP_HUB_GITHUB_CLIENT_ID=your-github-client-id
AI4SE_MCP_HUB_GITHUB_CLIENT_SECRET=your-github-client-secret

# Google OAuth App settings (optional)
AI4SE_MCP_HUB_GOOGLE_CLIENT_ID=your-google-client-id
AI4SE_MCP_HUB_GOOGLE_CLIENT_SECRET=your-google-client-secret

# OAuth Base URL (used to generate provider-specific callback URLs)
# Example: http://localhost:8000 will generate:
# - GitHub: http://localhost:8000/api/v1/auth/oauth/github/callback
# - Google: http://localhost:8000/api/v1/auth/oauth/google/callback
AI4SE_MCP_HUB_OAUTH_BASE_URL=http://localhost:8000